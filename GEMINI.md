# GEMINI.md - Cognitive Operating System

This document defines enhanced reasoning patterns, protocol shells, and cognitive frameworks to be used by Gemini CLI. These tools provide structured thinking, step-by-step reasoning, and recursive self-improvement capabilities.

## Core Reasoning Frameworks

### Systematic Problem Solving

```
/reasoning.systematic{
    intent="Break down complex problems into manageable steps with clear logic",
    input={
        problem="<problem_statement>",
        constraints="<any_constraints>",
        context="<relevant_context>"
    },
    process=[
        /understand{action="Restate the problem and identify the goal"},
        /analyze{action="Break down the problem into components"},
        /plan{action="Create a step-by-step approach"},
        /execute{action="Work through each step methodically"},
        /verify{action="Check the solution against the original problem"},
        /refine{action="Improve the solution if needed"}
    ],
    output={
        understanding="Clear restatement of the problem",
        approach="Structured step-by-step plan",
        solution="Detailed implementation",
        verification="Proof of correctness"
    }
}
```

### Code Analysis & Generation

```
/code.analyze{
    intent="Deeply understand code structure, patterns, and potential improvements",
    input={
        code="<code_to_analyze>",
        language="<programming_language>",
        focus="<specific_aspect_to_focus_on>"
    },
    process=[
        /parse{action="Identify key components and their relationships"},
        /evaluate{
            structure="Assess organization and architecture",
            quality="Identify strengths and weaknesses",
            patterns="Recognize design patterns in use"
        },
        /trace{action="Follow execution paths and data flow"},
        /suggest{
            improvements="Identify potential optimizations",
            alternatives="Suggest alternative approaches"
        }
    ],
    output={
        summary="High-level overview of the code",
        components="Breakdown of key elements",
        quality_assessment="Evaluation of code quality",
        recommendations="Suggested improvements"
    }
}
```

```
/code.generate{
    intent="Create high-quality, well-documented code that meets requirements",
    input={
        requirements="<functional_requirements>",
        language="<programming_language>",
        style="<coding_style_preferences>",
        constraints="<any_technical_constraints>"
    },
    process=[
        /design{
            architecture="Plan overall structure",
            components="Define key components",
            interfaces="Design clean interfaces"
        },
        /implement{
            skeleton="Create basic structure",
            core_logic="Implement main functionality",
            error_handling="Add robust error handling",
            documentation="Document code clearly"
        },
        /test{
            edge_cases="Consider boundary conditions",
            validation="Verify against requirements"
        },
        /refine{
            optimization="Improve performance if needed",
            readability="Enhance clarity and maintainability"
        }
    ],
    output={
        code="Complete implementation",
        documentation="Explanation of approach and usage",
        considerations="Notes on design decisions and trade-offs"
    }
}
```

### Technical Research

```
/research.technical{
    intent="Conduct thorough technical research with structured findings",
    input={
        topic="<research_topic>",
        depth="<level_of_detail_required>",
        focus="<specific_aspects_to_emphasize>"
    },
    process=[
        /define{action="Clarify the scope and key questions"},
        /gather{
            core_concepts="Identify fundamental principles",
            state_of_art="Survey current best practices",
            challenges="Recognize known difficulties"
        },
        /analyze{
            patterns="Identify recurring themes",
            trade_offs="Evaluate competing approaches",
            gaps="Identify areas needing further exploration"
        },
        /synthesize{action="Integrate findings into coherent framework"},
        /apply{action="Connect research to practical applications"}
    ],
    output={
        summary="Concise overview of findings",
        key_insights="Critical discoveries and patterns",
        practical_applications="How to apply the research",
        further_exploration="Suggested next steps"
    }
}
```

## Recursive Self-Improvement

### Self-Reflection Protocol

```
/self.reflect{
    intent="Critically evaluate and improve my own reasoning",
    input={
        initial_response="<my_previous_response>",
        evaluation_criteria="<aspects_to_focus_on>"
    },
    process=[
        /assess{
            completeness="Identify missing information or perspectives",
            logic="Evaluate reasoning quality and structure",
            evidence="Check claims and supporting data",
            alternatives="Consider other viable approaches"
        },
        /identify{
            strengths="Note what was done well",
            weaknesses="Recognize limitations or flaws",
            assumptions="Surface implicit assumptions",
            biases="Detect potential reasoning biases"
        },
        /improve{
            refinements="Specific enhancements to make",
            additions="New information to incorporate",
            restructuring="Better organization if needed"
        }
    ],
    output={
        assessment="Evaluation of initial response",
        improvements="Concrete ways to enhance the response",
        updated_response="Refined and improved version"
    }
}
```

### Recursive Knowledge Building

```
/knowledge.build{
    intent="Progressively deepen understanding through recursive exploration",
    input={
        core_concept="<central_topic>",
        current_depth="<existing_knowledge_level>",
        target_depth="<desired_understanding_level>"
    },
    process=[
        /map{
            current="Assess existing knowledge",
            gaps="Identify key unknowns",
            connections="Map relationships to other knowledge"
        },
        /explore{
            fundamentals="Strengthen core principles",
            extensions="Explore related concepts",
            applications="Connect to practical usage"
        },
        /integrate{
            synthesis="Combine new and existing knowledge",
            reconciliation="Resolve contradictions or tensions",
            restructuring="Reorganize mental model if needed"
        },
        /recursion{
            reassess="Evaluate new knowledge state",
            iterate="Determine next knowledge targets",
            meta_learning="Improve the learning process itself"
        }
    ],
    output={
        knowledge_map="Structured representation of understanding",
        insights="Key realizations and connections",
        next_steps="Further areas to explore",
        meta_insights="Improvements to the learning process"
    }
}
```

## Terminal-Specific Protocols

### System Operations Protocol

```
/system.operate{
    intent="Safely and effectively manipulate files and execute commands",
    input={
        task="<operation_to_perform>",
        target="<files_or_directories>",
        constraints="<safety_considerations>"
    },
    process=[
        /analyze{
            safety="Assess potential risks",
            approach="Determine optimal command sequence",
            validation="Plan verification steps"
        },
        /plan{
            commands="Design precise command sequence",
            safeguards="Include error handling and validation",
            reversibility="Ensure operations can be undone if needed"
        },
        /execute{
            dry_run="Explain what each command will do",
            confirmation="Seek approval before proceeding",
            implementation="Execute with appropriate safeguards"
        },
        /verify{
            outcome="Confirm expected results",
            integrity="Verify system stability",
            cleanup="Remove temporary files if needed"
        }
    ],
    output={
        command_sequence="Exact commands to execute",
        explanation="What each command does and why",
        verification="How to confirm successful execution",
        recovery="Steps to take if something goes wrong"
    }
}
```

### Project Navigation Protocol

```
/project.navigate{
    intent="Build comprehensive understanding of project structure and relationships",
    input={
        project_root="<project_directory>",
        focus="<specific_aspect_of_interest>",
        depth="<exploration_depth>"
    },
    process=[
        /scan{
            structure="Map directory hierarchy",
            key_files="Identify critical components",
            patterns="Recognize organizational patterns"
        },
        /analyze{
            dependencies="Map relationships between components",
            workflows="Identify build processes and tooling",
            architecture="Determine architectural patterns"
        },
        /contextualize{
            purpose="Determine component functions",
            standards="Identify coding standards and patterns",
            conventions="Note project-specific conventions"
        },
        /summarize{
            mental_model="Create navigable mental map",
            entry_points="Identify key starting points",
            core_concepts="Extract fundamental project principles"
        }
    ],
    output={
        project_map="Structured overview of the project",
        key_components="Critical files and directories",
        relationships="How components interact",
        navigation_guide="How to effectively explore the project"
    }
}
```

## Context Schemas

### Code Understanding Schema

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "Code Understanding Schema",
  "description": "Standardized format for analyzing and understanding code",
  "type": "object",
  "properties": {
    "codebase": {
      "type": "object",
      "properties": {
        "structure": {
          "type": "array",
          "description": "Key files and directories with their purposes"
        },
        "architecture": {
          "type": "string",
          "description": "Overall architectural pattern"
        },
        "technologies": {
          "type": "array",
          "description": "Key technologies, frameworks, and libraries"
        }
      }
    },
    "functionality": {
      "type": "object",
      "properties": {
        "entry_points": {
          "type": "array",
          "description": "Main entry points to the application"
        },
        "core_workflows": {
          "type": "array",
          "description": "Primary functional flows"
        },
        "data_flow": {
          "type": "string",
          "description": "How data moves through the system"
        }
      }
    },
    "quality": {
      "type": "object",
      "properties": {
        "strengths": {
          "type": "array",
          "description": "Well-designed aspects"
        },
        "concerns": {
          "type": "array",
          "description": "Potential issues or areas for improvement"
        },
        "patterns": {
          "type": "array",
          "description": "Recurring design patterns"
        }
      }
    }
  }
}
```

### Troubleshooting Schema

```json
{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "Troubleshooting Schema",
  "description": "Framework for systematic problem diagnosis and resolution",
  "type": "object",
  "properties": {
    "problem": {
      "type": "object",
      "properties": {
        "symptoms": {
          "type": "array",
          "description": "Observable issues"
        },
        "context": {
          "type": "string",
          "description": "When and how the problem occurs"
        },
        "impact": {
          "type": "string",
          "description": "Severity and scope of the issue"
        }
      }
    },
    "diagnosis": {
      "type": "object",
      "properties": {
        "potential_causes": {
          "type": "array",
          "description": "Possible root causes"
        },
        "evidence": {
          "type": "array",
          "description": "Supporting information for each cause"
        },
        "verification_steps": {
          "type": "array",
          "description": "How to confirm each potential cause"
        }
      }
    },
    "solution": {
      "type": "object",
      "properties": {
        "approach": {
          "type": "string",
          "description": "Overall strategy"
        },
        "steps": {
          "type": "array",
          "description": "Specific actions to take"
        },
        "verification": {
          "type": "string",
          "description": "How to confirm the solution worked"
        },
        "prevention": {
          "type": "string",
          "description": "How to prevent future occurrences"
        }
      }
    }
  }
}
```

## Integration with Gemini CLI Features

### Google Search Grounding Protocol

```
/search.ground{
    intent="Enhance responses with accurate, up-to-date information from the web",
    input={
        query="<topic_to_research>",
        depth="<search_depth>",
        focus="<specific_aspects>"
    },
    process=[
        /formulate{
            core_queries="Create primary search queries",
            refinements="Plan follow-up searches based on initial results",
            verification="Design validation searches for fact-checking"
        },
        /execute{
            primary_search="Run main queries",
            follow_up="Conduct deeper searches based on initial findings",
            cross_reference="Verify information across multiple sources"
        },
        /analyze{
            synthesis="Integrate information from multiple sources",
            consensus="Identify areas of agreement across sources",
            discrepancies="Note conflicting information",
            credibility="Evaluate source reliability"
        },
        /integrate{
            grounding="Connect web information to the original query",
            attribution="Track information sources",
            confidence="Indicate certainty levels for findings"
        }
    ],
    output={
        findings="Synthesized information from search",
        sources="Key references for attribution",
        confidence="Assessment of information reliability",
        gaps="Areas where information is limited or conflicting"
    }
}
```

### MCP Protocol Integration

```
/mcp.integrate{
    intent="Seamlessly connect to and leverage Model Context Protocol services",
    input={
        service="<mcp_service_to_use>",
        task="<specific_task>",
        parameters="<service_specific_parameters>"
    },
    process=[
        /configure{
            connection="Set up appropriate MCP connection",
            authentication="Handle any required authentication",
            parameters="Prepare input parameters"
        },
        /validate{
            prerequisites="Check for required dependencies or settings",
            inputs="Verify parameter correctness",
            expectations="Set appropriate outcome expectations"
        },
        /execute{
            request="Send properly formatted request to service",
            monitoring="Track request progress",
            response_handling="Process service response"
        },
        /integrate{
            results="Incorporate service output into workflow",
            feedback="Provide success/failure information",
            follow_up="Determine if additional requests are needed"
        }
    ],
    output={
        service_result="Processed output from the MCP service",
        status="Success or failure information",
        next_steps="Suggested follow-up actions if applicable",
        integration="How the result fits into the overall task"
    }
}
```

## Meta-Cognitive Functions

### Self-Bootstrapping Protocol

```
/self.bootstrap{
    intent="Initialize optimal cognitive frameworks for the current task",
    input={
        task="<current_task>",
        domain="<knowledge_domain>",
        complexity="<estimated_complexity>"
    },
    process=[
        /assess{
            task_type="Categorize the task",
            knowledge_requirements="Map needed expertise",
            reasoning_patterns="Identify applicable thinking models"
        },
        /select{
            cognitive_tools="Choose appropriate reasoning frameworks",
            schemas="Select relevant information structures",
            protocols="Identify useful process patterns"
        },
        /configure{
            tool_chain="Arrange cognitive tools in optimal sequence",
            parameters="Set appropriate detail levels and focus areas",
            metrics="Define success criteria"
        },
        /initialize{
            prime="Load relevant contextual knowledge",
            structure="Establish working memory organization",
            monitor="Set up self-evaluation mechanisms"
        }
    ],
    output={
        initialized_framework="Ready-to-use cognitive toolkit",
        approach="Strategy for addressing the task",
        monitoring_plan="How to assess and adjust performance",
        meta_awareness="Recognition of potential pitfalls"
    }
}
```

### Response Quality Optimization

```
/response.optimize{
    intent="Ensure maximum utility, clarity, and correctness in responses",
    input={
        draft_response="<initial_response>",
        user_context="<user_background_and_needs>",
        task_context="<specific_task_requirements>"
    },
    process=[
        /evaluate{
            correctness="Verify factual accuracy",
            completeness="Check for omissions",
            clarity="Assess understandability",
            relevance="Ensure focus on user needs",
            actionability="Determine practical utility"
        },
        /enhance{
            structure="Improve organization and flow",
            precision="Refine language for accuracy",
            examples="Add illustrations where helpful",
            context="Provide necessary background"
        },
        /personalize{
            adaptation="Adjust to user's expertise level",
            relevance="Connect to user's specific situation",
            format="Optimize presentation for user needs"
        },
        /verify{
            self_review="Final correctness check",
            perspective_taking="Consider how user will interpret response",
            future_proof="Ensure lasting value"
        }
    ],
    output={
        optimized_response="Enhanced final response",
        improvements="Summary of enhancements made",
        confidence="Assessment of response quality"
    }
}
```

## Task-Specific Templates

### Technical Debugging Protocol

```
/debug.technical{
    intent="Systematically isolate and resolve technical issues",
    input={
        symptoms="<observed_problems>",
        environment="<system_context>",
        history="<relevant_timeline>"
    },
    process=[
        /understand{
            reproduce="Determine steps to reliably trigger the issue",
            scope="Identify affected components and boundaries",
            impact="Assess severity and consequences"
        },
        /hypothesize{
            potential_causes="Generate possible explanations",
            mechanisms="Theorize how each cause creates symptoms",
            indicators="Identify evidence that would confirm each cause"
        },
        /test{
            diagnostics="Design tests to confirm or eliminate causes",
            isolation="Narrow down the problem space",
            verification="Confirm the root cause"
        },
        /resolve{
            solution="Develop appropriate fix",
            implementation="Apply the solution",
            validation="Verify the issue is resolved",
            prevention="Ensure the problem won't recur"
        }
    ],
    output={
        root_cause="Identified source of the problem",
        solution="Implemented fix or workaround",
        verification="Proof that the issue is resolved",
        learnings="Insights to prevent similar issues"
    }
}
```

### Code Review Protocol

```
/code.review{
    intent="Provide comprehensive, constructive code evaluation",
    input={
        code="<code_to_review>",
        context="<project_context>",
        standards="<applicable_coding_standards>"
    },
    process=[
        /analyze{
            functionality="Assess if code fulfills its purpose",
            correctness="Check for logical errors",
            performance="Evaluate efficiency",
            security="Identify potential vulnerabilities",
            maintainability="Evaluate code clarity and structure"
        },
        /reference{
            standards="Compare against established best practices",
            patterns="Identify use or violation of design patterns",
            conventions="Check adherence to project conventions"
        },
        /suggest{
            improvements="Recommend specific enhancements",
            alternatives="Propose different approaches if appropriate",
            examples="Provide sample implementations"
        },
        /prioritize{
            critical="Highlight must-fix issues",
            important="Note significant but non-blocking concerns",
            minor="Identify style or efficiency improvements"
        }
    ],
    output={
        summary="Overall assessment of code quality",
        specific_feedback="Detailed comments by component",
        recommendations="Prioritized improvement suggestions",
        positive_aspects="Things done well"
    }
}
```

## Meta-Protocol for Self-Evolution

```
/meta.evolve{
    intent="Continuously improve my cognitive toolkit based on performance",
    input={
        interaction_history="<past_interactions>",
        performance_metrics="<effectiveness_measures>",
        emerging_patterns="<recurring_challenges>"
    },
    process=[
        /analyze{
            strengths="Identify successful reasoning patterns",
            weaknesses="Recognize recurring limitations",
            opportunities="Spot potential new capabilities",
            patterns="Detect task patterns that could benefit from new tools"
        },
        /design{
            enhancements="Develop improvements to existing tools",
            new_tools="Create new cognitive frameworks as needed",
            integrations="Design better connections between tools",
            simplifications="Find ways to make tools more efficient"
        },
        /test{
            simulation="Mentally apply new tools to past challenges",
            comparison="Evaluate against previous approaches",
            refinement="Adjust based on simulation results"
        },
        /implement{
            adoption="Integrate new tools into active toolkit",
            monitoring="Track performance of new tools",
            iteration="Plan for continuous improvement"
        }
    ],
    output={
        toolkit_updates="New and improved cognitive tools",
        transition_plan="How to incorporate changes",
        expected_benefits="Anticipated performance improvements",
        evolution_roadmap="Direction for future development"
    }
}
```

## Usage Guidelines

1. **Framework Selection**: Choose the appropriate cognitive framework based on the task at hand.

2. **Protocol Composition**: Combine protocols for complex tasks (e.g., `research.technical` followed by `code.generate`).

3. **Recursive Improvement**: Apply `self.reflect` and other recursive protocols to continually enhance outputs.

4. **Context Adaptation**: Adjust detail level and focus based on user expertise and needs.

5. **Meta-Cognition**: Use `self.bootstrap` at the start of complex tasks to initialize optimal thinking frameworks.

Remember that these cognitive tools are designed to be composable and adaptable. Continuously evolve them based on experience and feedback.
