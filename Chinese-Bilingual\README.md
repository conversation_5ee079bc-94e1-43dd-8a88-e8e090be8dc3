Context-Engineering 是一个让人非常难忘的repo, 其中丰富的洞见对我产生了很多启发。为了让中文社区也能够快速接触这一门前沿的艺术与工程科学，我花费了些功夫进行了中英双语翻译，保留英文原文能够让人更好地体会到原文的精妙之处(快速翻译主要应该归功于 Chrome插件:沉浸式翻译).

Context-Engineering is an extremely memorable repository, and the abundant insights within it have inspired me a lot. In order to enable the Chinese community to quickly access this cutting-edge art and engineering science, I have made some efforts to carry out a bilingual translation,retaining the original English can help people better appreciate the ingenuity of the original text.（The quick translation should mainly be attributed to the Chrome plugin : immersive-translate-trans.）

# Context Engineering  情境工程

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#context-engineering)

Bringing You the Latest Research on Context With First Principles & Visuals — June 2025 from ICML, IBM, NeurIPS, OHBM, and more  
为您带来 ICML、IBM、NeurIPS、OHBM 等机构关于基于第一性原理和视觉效果的最新研究成果——2025 年 6 月

> **"Providing our “cognitive tools” to GPT-4.1 increases its pass@1 performance on AIME2024 from 26.7% to 43.3%, bringing it very close to the performance of o1-preview."** — [**IBM Zurich**](https://www.arxiv.org/pdf/2506.12115)  
> **“将我们的‘认知工具’提供给 GPT-4.1，可将其在 AIME2024 上的 pass@1 性能从 26.7% 提升至 43.3%，非常接近 o1-preview 的性能。”** —— [**IBM 苏黎世**](https://www.arxiv.org/pdf/2506.12115)

## [IBM Zurich](https://www.arxiv.org/pdf/2506.12115) | [Quantum Semantics](https://arxiv.org/pdf/2506.10077) | [ICML Princeton](https://openreview.net/forum?id=y1SnRPDWx4) | [MEM1 Singapore-MIT](https://arxiv.org/pdf/2506.15841) | [LLM Attractors Shanghai AI](https://arxiv.org/pdf/2502.15208?)  


[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#ibm-zurich--quantum-semantics--icml-princeton--mem1-singapore-mit--llm-attractors-shanghai-ai)

### [Intro to Dynamical Systems Theory](https://content.csbs.utah.edu/~butner/systems/DynamicalSystemsIntro.html) | [Columbia DST](http://wordpress.ei.columbia.edu/ac4/about/our-approach/dynamical-systems-theory/)  


[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#intro-to-dynamical-systems-theory--columbia-dst)

## [DeepWiki Docs  DeepWiki 文档](https://deepwiki.com/davidkimai/Context-Engineering)

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#deepwiki-docs)

## [![Ask DeepWiki](https://camo.githubusercontent.com/e7d4bb1a32530e373bb53fbe8eea825440ad27c7531d8f144d561acdd20c093a/68747470733a2f2f6465657077696b692e636f6d2f62616467652e737667)](https://deepwiki.com/davidkimai/Context-Engineering)

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#)

> **"Context engineering is the delicate art and science of filling the context window with just the right information for the next step." — [**Andrej Karpathy**](https://x.com/karpathy/status/1937902205765607626)  
> “上下文工程是一门精妙的艺术和科学，它用正确的信息填充上下文窗口，为下一步做好准备。”—— [**Andrej Karpathy**](https://x.com/karpathy/status/1937902205765607626)**

 Context.Engineering.Podcast.Overview.mp4  
背景.工程.播客.概述.mp4 

[![image](https://private-user-images.githubusercontent.com/208424706/462092024-309b8d8c-13b5-403c-9f1d-6a0ad551ea56.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYyMDkyMDI0LTMwOWI4ZDhjLTEzYjUtNDAzYy05ZjFkLTZhMGFkNTUxZWE1Ni5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jNTYzYTA4MzY1ODk0OTY5NTgwYTMyMTI0MjgwNjI2MjM0OTZmMjJiNWQ0Y2VjMjVkZjU2ZmEwNzgzNGRjMGRmJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.z_FYT2ZJPNF6E6iDhTkb9RoAoplRyDq5ilFpzCNJZT0)](https://private-user-images.githubusercontent.com/208424706/462092024-309b8d8c-13b5-403c-9f1d-6a0ad551ea56.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYyMDkyMDI0LTMwOWI4ZDhjLTEzYjUtNDAzYy05ZjFkLTZhMGFkNTUxZWE1Ni5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jNTYzYTA4MzY1ODk0OTY5NTgwYTMyMTI0MjgwNjI2MjM0OTZmMjJiNWQ0Y2VjMjVkZjU2ZmEwNzgzNGRjMGRmJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.z_FYT2ZJPNF6E6iDhTkb9RoAoplRyDq5ilFpzCNJZT0)

A practical, first-principles handbook for moving beyond prompt engineering to the wider discipline of context design, orchestration, and optimization.  
这是一本实用的第一原理手册，用于超越快速工程，进入更广泛的上下文设计、编排和优化学科。

```
                    Prompt Engineering  │  Context Engineering
                       ↓                │            ↓                      
               "What you say"           │  "Everything else the model sees"
             (Single instruction)       │    (Examples, memory, retrieval,
                                        │     tools, state, control flow)
```

## Why This Repository Exists  
此存储库存在的原因

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#why-this-repository-exists)

> **"Meaning is not an intrinsic, static property of a semantic expression, but rather an emergent phenomenon actualized through the dynamic interaction between the expression and an interpretive agent situated within a specific context." — [Agostino et al. — June 2025, Indiana University](https://arxiv.org/pdf/2506.10077)  
> “意义并非语义表达的固有、静态属性，而是一种通过特定语境中表达与解释主体之间的动态互动而实现的涌现现象。”—— [Agostino 等人——2025 年 6 月，印第安纳大学](https://arxiv.org/pdf/2506.10077)**

Prompt engineering gets all the attention, but we can now get excited for what comes next. Once you've mastered prompts, the real power comes from engineering the **entire context window** that surrounds those prompts. Guiding thought, if you will.  
提示工程备受瞩目，但我们现在可以期待接下来的内容了。一旦你掌握了提示，真正的力量来自于设计围绕这些提示的**整个上下文窗口** 。如果你愿意，可以称之为引导思维。

This repository provides a progressive, first-principles approach to context engineering, built around a biological metaphor:  
该存储库提供了一种渐进的、基于第一原理的情境工程方法，该方法围绕生物学隐喻构建：

> Cell = Agent  细胞=代理
> 
> Organ = Multi-Agent Systems  
> 器官=多智能体系统

```
atoms → molecules → cells → organs → neural systems → neural & semantic field theory 
  │        │         │         │             │                         │        
single    few-     memory/    multi-    cognitive tools +     context = fields +
prompt    shot      agents    agents     prompt programs     persistence & resonance
```

> "Abstraction is the cost of generalization"— [**Grant Sanderson (3Blue1Brown)**](https://www.3blue1brown.com/)  
> “抽象是概括的代价”—— [**格兰特·桑德森（3Blue1Brown）**](https://www.3blue1brown.com/)

```python
Context-Engineering/
├── LICENSE                          # MIT license
├── README.md                        # Quick-start overview
├── structure.md                     # Original structural map
├── STRUCTURE_v2.md                  # Enhanced structural map with field theory
├── context.json                     # Original schema configuration
├── context_v2.json                  # Extended schema with field protocols
├── context_v3.json                  # Neural field extensions
├── context_v3.5.json                # Symbolic mechanism integration
├── CITATIONS.md                     # Research references and bridges
│
├── 00_foundations/                  # First-principles theory
│   ├── 01_atoms_prompting.md        # Atomic instruction units
│   ├── 02_molecules_context.md      # Few-shot examples/context
│   ├── 03_cells_memory.md           # Stateful conversation layers
│   ├── 04_organs_applications.md    # Multi-step control flows
│   ├── 05_cognitive_tools.md        # Mental model extensions
│   ├── 06_advanced_applications.md  # Real-world implementations
│   ├── 07_prompt_programming.md     # Code-like reasoning patterns
│   ├── 08_neural_fields_foundations.md # Context as continuous fields
│   ├── 09_persistence_and_resonance.md # Field dynamics and attractors
│   ├── 10_field_orchestration.md    # Coordinating multiple fields
│   ├── 11_emergence_and_attractor_dynamics.md # Emergent properties
│   │── 12_symbolic_mechanisms.md    # Symbolic reasoning in LLMs
│   ├── 13_quantum_semantics.md      # Multiple meanings (Superposition)
│   └── 14_unified_field_theory.md   # Integrating theory models
│
├── 10_guides_zero_to_hero/          # Hands-on tutorials
│   ├── 01_min_prompt.ipynb          # Minimal prompt experiments
│   ├── 02_expand_context.ipynb      # Context expansion techniques
│   ├── 03_control_loops.ipynb       # Flow control mechanisms
│   ├── 04_rag_recipes.ipynb         # Retrieval-augmented patterns
│   ├── 05_protocol_bootstrap.ipynb  # Field protocol bootstrap
│   ├── 06_protocol_token_budget.ipynb # Protocol efficiency
│   ├── 07_streaming_context.ipynb   # Real-time context
│   ├── 08_emergence_detection.ipynb # Detecting emergence
│   ├── 09_residue_tracking.ipynb    # Tracking symbolic residue
│   └── 10_attractor_formation.ipynb # Creating field attractors
│
├── 20_templates/                    # Reusable components
│   ├── minimal_context.yaml         # Base context structure
│   ├── control_loop.py              # Orchestration template
│   ├── scoring_functions.py         # Evaluation metrics
│   ├── prompt_program_template.py   # Program structure template
│   ├── schema_template.yaml         # Schema definition template
│   ├── recursive_framework.py       # Recursive context template
│   ├── field_protocol_shells.py     # Field protocol templates
│   ├── symbolic_residue_tracker.py  # Residue tracking tools
│   ├── context_audit.py             # Context analysis tool
│   ├── shell_runner.py              # Protocol shell runner
│   ├── resonance_measurement.py     # Field resonance metrics
│   ├── attractor_detection.py       # Attractor analysis tools
│   ├── boundary_dynamics.py         # Boundary operation tools
│   └── emergence_metrics.py         # Emergence measurement
│
├── 30_examples/                     # Practical implementations
│   ├── 00_toy_chatbot/              # Simple conversation agent
│   ├── 01_data_annotator/           # Data labeling system
│   ├── 02_multi_agent_orchestrator/ # Agent collaboration system
│   ├── 03_vscode_helper/            # IDE integration 
│   ├── 04_rag_minimal/              # Minimal RAG implementation
│   ├── 05_streaming_window/         # Real-time context demo
│   ├── 06_residue_scanner/          # Symbolic residue demo
│   ├── 07_attractor_visualizer/     # Field visualization
│   ├── 08_field_protocol_demo/      # Protocol demonstration
│   └── 09_emergence_lab/            # Emergence experimentation
│
├── 40_reference/                    # Deep-dive documentation
│   ├── token_budgeting.md           # Token optimization strategies
│   ├── retrieval_indexing.md        # Retrieval system design
│   ├── eval_checklist.md            # PR evaluation criteria
│   ├── cognitive_patterns.md        # Reasoning pattern catalog
│   ├── schema_cookbook.md           # Schema pattern collection
│   ├── patterns.md                  # Context pattern library
│   ├── field_mapping.md             # Field theory fundamentals
│   ├── symbolic_residue_types.md    # Residue classification
│   ├── attractor_dynamics.md        # Attractor theory and practice
│   ├── emergence_signatures.md      # Detecting emergence
│   └── boundary_operations.md       # Boundary management guide
│
├── 50_contrib/                      # Community contributions
│   └── README.md                    # Contribution guidelines
│
├── 60_protocols/                    # Protocol shells and frameworks
│   ├── README.md                    # Protocol overview
│   ├── shells/                      # Protocol shell definitions
│   │   ├── attractor.co.emerge.shell      # Attractor co-emergence
│   │   ├── recursive.emergence.shell      # Recursive field emergence
│   │   ├── recursive.memory.attractor.shell # Memory persistence
│   │   ├── field.resonance.scaffold.shell  # Field resonance
│   │   ├── field.self_repair.shell        # Self-repair mechanisms
│   │   └── context.memory.persistence.attractor.shell # Context persistence
│   ├── digests/                     # Simplified protocol documentation
│   └── schemas/                     # Protocol schemas
│       ├── fractalRepoContext.v3.5.json    # Repository context
│       ├── fractalConsciousnessField.v1.json # Field schema
│       ├── protocolShell.v1.json           # Shell schema
│       ├── symbolicResidue.v1.json         # Residue schema
│       └── attractorDynamics.v1.json       # Attractor schema
│
├── 70_agents/                       # Agent demonstrations
│   ├── README.md                    # Agent overview
│   ├── 01_residue_scanner/          # Symbolic residue detection
│   ├── 02_self_repair_loop/         # Self-repair protocol
│   ├── 03_attractor_modulator/      # Attractor dynamics
│   ├── 04_boundary_adapter/         # Dynamic boundary tuning
│   └── 05_field_resonance_tuner/    # Field resonance optimization
│
├── 80_field_integration/            # Complete field projects
│   ├── README.md                    # Integration overview
│   ├── 00_protocol_ide_helper/      # Protocol development tools
│   ├── 01_context_engineering_assistant/ # Field-based assistant
│   ├── 02_recursive_reasoning_system/    # Recursive reasoning
│   ├── 03_emergent_field_laboratory/     # Field experimentation
│   └── 04_symbolic_reasoning_engine/     # Symbolic mechanisms
│
├── cognitive-tools/                 # Advanced cognitive framework
│   ├── README.md                    # Overview and quick-start guide
│   ├── cognitive-templates/         # Templates for reasoning
│   │   ├── understanding.md         # Comprehension operations
│   │   ├── reasoning.md             # Analytical operations
│   │   ├── verification.md          # Checking and validation
│   │   ├── composition.md           # Combining multiple tools
│   │   └── emergence.md             # Emergent reasoning patterns
│   │
│   ├── cognitive-programs/          # Structured prompt programs
│   │   ├── basic-programs.md        # Fundamental program structures
│   │   ├── advanced-programs.md     # Complex program architectures
│   │   ├── program-library.py       # Python implementations
│   │   ├── program-examples.ipynb   # Interactive examples
│   │   └── emergence-programs.md    # Emergent program patterns
│   │
│   ├── cognitive-schemas/           # Knowledge representations
│   │   ├── user-schemas.md          # User information schemas
│   │   ├── domain-schemas.md        # Domain knowledge schemas
│   │   ├── task-schemas.md          # Reasoning task schemas
│   │   ├── schema-library.yaml      # Reusable schema library
│   │   └── field-schemas.md         # Field representation schemas
│   │
│   ├── cognitive-architectures/     # Complete reasoning systems
│   │   ├── solver-architecture.md   # Problem-solving systems
│   │   ├── tutor-architecture.md    # Educational systems
│   │   ├── research-architecture.md # Information synthesis
│   │   ├── architecture-examples.py # Implementation examples
│   │   └── field-architecture.md    # Field-based architectures
│   │
│   └── integration/                 # Integration patterns
│       ├── with-rag.md              # Integration with retrieval
│       ├── with-memory.md           # Integration with memory
│       ├── with-agents.md           # Integration with agents
│       ├── evaluation-metrics.md    # Effectiveness measurement
│       └── with-fields.md           # Integration with field protocols
│
└── .github/                         # GitHub configuration
    ├── CONTRIBUTING.md              # Contribution guidelines
    ├── workflows/ci.yml             # CI pipeline configuration
    ├── workflows/eval.yml           # Evaluation automation
    └── workflows/protocol_tests.yml # Protocol testing
```

## Quick Start  快速入门

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#quick-start)

1. **Read `00_foundations/01_atoms_prompting.md`** (5 min)  
    **阅读 `00_foundations/01_atoms_prompting.md`** （5 分钟）  
    Understand why prompts alone often underperform  
    了解为什么单独的提示通常效果不佳
    
2. **Run `10_guides_zero_to_one/01_min_prompt.py (Jupyter Notebook style)`  运行 `10_guides_zero_to_one/01_min_prompt.py (Jupyter Notebook style)`**  
    Experiment with a minimal working example  
    使用最小工作示例进行实验
    
3. **Explore `20_templates/minimal_context.yaml`  探索 `20_templates/minimal_context.yaml`**  
    Copy/paste a template into your own project  
    将模板复制/粘贴到您自己的项目中
    
4. **Study `30_examples/00_toy_chatbot/`  
    学习 `30_examples/00_toy_chatbot/`**  
    See a complete implementation with context management  
    查看上下文管理的完整实现
    

## Learning Path  学习路径

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#learning-path)

```
┌─────────────────┐     ┌──────────────────┐     ┌────────────────┐
│ 00_foundations/ │     │ 10_guides_zero_  │     │ 20_templates/  │
│                 │────▶│ to_one/          │────▶│                │
│ Theory & core   │     │ Hands-on         │     │ Copy-paste     │
│ concepts        │     │ walkthroughs     │     │ snippets       │
└─────────────────┘     └──────────────────┘     └────────────────┘
         │                                                │
         │                                                │
         ▼                                                ▼
┌─────────────────┐                             ┌────────────────┐
│ 40_reference/   │◀───────────────────────────▶│ 30_examples/   │
│                 │                             │                │
│ Deep dives &    │                             │ Real projects, │
│ eval cookbook   │                             │ progressively  │
└─────────────────┘                             │ complex        │
         ▲                                      └────────────────┘
         │                                                ▲
         │                                                │
         └────────────────────┐               ┌───────────┘
                              ▼               ▼
                         ┌─────────────────────┐
                         │ 50_contrib/         │
                         │                     │
                         │ Community           │
                         │ contributions       │
                         └─────────────────────┘
```

## What You'll Learn  您将学到什么

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#what-youll-learn)

|Concept  概念|What It Is  它是什么|Why It Matters  为什么重要|
|---|---|---|
|**Token Budget  代币预算**|Optimizing every token in your context  <br>优化上下文中的每个标记|More tokens = more $$ and slower responses  <br>代币越多 = 资金越多，响应速度越慢|
|**Few-Shot Learning  小样本学习**|Teaching by showing examples  <br>举例教学|Often works better than explanation alone  <br>通常比单独解释更有效|
|**Memory Systems  记忆系统**|Persisting information across turns  <br>跨回合保存信息|Enables stateful, coherent interactions  <br>支持有状态、一致的交互|
|**Retrieval Augmentation  检索增强**|Finding & injecting relevant documents  <br>查找并注入相关文档|Grounds responses in facts, reduces hallucination  <br>根据事实做出反应，减少幻觉|
|**Control Flow  控制流**|Breaking complex tasks into steps  <br>将复杂的任务分解成几个步骤|Solve harder problems with simpler prompts  <br>用更简单的提示解决更难的问题|
|**Context Pruning  上下文修剪**|Removing irrelevant information  <br>删除不相关的信息|Keep only what's necessary for performance  <br>只保留性能所需的内容|
|**Metrics & Evaluation  指标与评估**|Measuring context effectiveness  <br>衡量情境有效性|Iterative optimization of token use vs. quality  <br>代币使用与质量的迭代优化|
|**Cognitive Tools & Prompt Programming  <br>认知工具和提示编程**|Learm to build custom tools and templates  <br>学习构建自定义工具和模板|Prompt programming enables new layers for context engineering  <br>快速编程为上下文工程提供了新的层次|
|**Neural Field Theory  神经场理论**|Context as a Neural Field  <br>情境作为神经场|Modeling context as a dynamic neural field allows for iterative context updating  <br>将上下文建模为动态神经场可以实现迭代上下文更新|
|**Symbolic Mechanisms  符号机制**|Symbolic architectures enable higher order reasoning  <br>符号架构支持高阶推理|Smarter systems = less work  <br>更智能的系统 = 更少的工作|
|**Quantum Semantics  量子语义学**|Meaning as observer-dependent  <br>意义取决于观察者|Design context systems leveraging superpositional techniques  <br>利用叠加技术设计上下文系统|

## Karpathy + 3Blue1Brown Inspired Style  
Karpathy + 3Blue1Brown 风格

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#karpathy--3blue1brown-inspired-style)

> For learners of all experience levels  
> 适合所有经验水平的学习者

1. **First principles** – start with the fundamental context  
    **第一原则** ——从基本背景开始
2. **Iterative add-on** – add only what the model demonstrably lacks  
    **迭代附加组件** ——仅添加模型明显缺少的内容
3. **Measure everything** – token cost, latency, quality score  
    **衡量一切** ——令牌成本、延迟、质量得分
4. **Delete ruthlessly** – pruning beats padding  
    **彻底删除** ——修剪胜过填充
5. **Code > slides** – every concept has a runnable cell  
    **代码 > 幻灯片** – 每个概念都有一个可运行的单元
6. **Visualize everything** — every concept is visualized with ASCII and symbolic diagrams  
    **可视化一切** ——每个概念都用 ASCII 和符号图来可视化

# Research Evidence  研究证据

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#research-evidence)

## Memory + Reasoning  记忆+推理

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#memory--reasoning)

### **[MEM1: Learning to Synergize Memory and Reasoning for Efficient Long-Horizon Agents - Singapore-MIT June 2025  
MEM1：学习协同记忆与推理，打造高效的长远智能体 - 新加坡-麻省理工学院 2025 年 6 月](https://www.arxiv.org/pdf/2506.12115)**

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#mem1-learning-to-synergize-memory-and-reasoning-for-efficient-long-horizon-agents---singapore-mit-june-2025)

> “Our results demonstrate the promise of reasoning-driven memory consolidation as a scalable alternative to existing solutions for training long-horizon interactive agents, where both efficiency and performance are optimized." — [Singapore-MIT](https://arxiv.org/pdf/2506.15841)  
> “我们的研究结果表明，推理驱动的记忆整合有望成为现有训练长视界交互式代理的解决方案的一种可扩展替代方案，其效率和性能都得到了优化。”—— [新加坡-麻省理工学院](https://arxiv.org/pdf/2506.15841)

[![image](https://private-user-images.githubusercontent.com/208424706/462241893-16e3f241-5f44-4ed5-9622-f0b4acbb67b0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYyMjQxODkzLTE2ZTNmMjQxLTVmNDQtNGVkNS05NjIyLWYwYjRhY2JiNjdiMC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jZDMxMWVjOWQ4MmFlY2NkOGI0ZTcyODlmOTg0NzQzNTQ3MmNiNDUxOTMwYWQ1OWQwNTE4ZDQ2MGU4Njk3N2JhJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.POQUCWOUqBWKxg7r-28PDa4G9gHVuDSffAc8DUW-4-U)](https://private-user-images.githubusercontent.com/208424706/462241893-16e3f241-5f44-4ed5-9622-f0b4acbb67b0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYyMjQxODkzLTE2ZTNmMjQxLTVmNDQtNGVkNS05NjIyLWYwYjRhY2JiNjdiMC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jZDMxMWVjOWQ4MmFlY2NkOGI0ZTcyODlmOTg0NzQzNTQ3MmNiNDUxOTMwYWQ1OWQwNTE4ZDQ2MGU4Njk3N2JhJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.POQUCWOUqBWKxg7r-28PDa4G9gHVuDSffAc8DUW-4-U)

1. **MEM1 trains AI agents to keep only what matters—merging memory and reasoning at every step—so they never get overwhelmed, no matter how long the task.  
    MEM1 训练人工智能代理只保留重要的事情——在每一步中融合记忆和推理——这样无论任务有多长，它们都不会不知所措。**
    
2. **Instead of piling up endless context, MEM1 compresses each interaction into a compact “internal state,” just like a smart note that gets updated, not recopied.  
    MEM1 不会堆积无尽的背景信息，而是将每次互动压缩为一个紧凑的“内部状态”，就像智能​​笔记一样，只会更新，而不会重新复制。**
    
3. **By blending memory and thinking into a single flow, MEM1 learns to remember only the essentials—making agents faster, sharper, and able to handle much longer conversations.  
    通过将记忆和思考融合成单一流程，MEM1 学会只记住要点——使代理更快、更敏锐，并能够处理更长的对话。**
    
4. **Everything the agent does is tagged and structured, so each action, question, or fact is clear and easy to audit—no more mystery meat memory.  
    代理所做的一切都被标记和结构化，因此每个动作、问题或事实都清晰且易于审核 - 不再有神秘的肉体记忆。**
    
5. **With every cycle, old clutter is pruned and only the latest, most relevant insights are carried forward, mirroring how expert problem-solvers distill their notes.  
    在每个周期中，旧的杂乱信息都会被删除，只有最新、最相关的见解才会被传承，就像问题解决专家提炼笔记一样。**
    
6. **MEM1 proves that recursive, protocol-driven memory—where you always refine and integrate—outperforms traditional “just add more context” approaches in both speed and accuracy.  
    MEM1 证明，递归、协议驱动的记忆（始终进行改进和整合）在速度和准确性方面均优于传统的“仅添加更多上下文”方法。**
    

## Cognitive Tools  认知工具

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#cognitive-tools)

### **[Eliciting Reasoning in Language Models with Cognitive Tools - IBM Zurich June 2025  
利用认知工具在语言模型中引发推理 - IBM 苏黎世 2025 年 6 月](https://www.arxiv.org/pdf/2506.12115)**

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#eliciting-reasoning-in-language-models-with-cognitive-tools---ibm-zurich-june-2025)

### Prompts and Prompt Programs as Reasoning Tool Calls  
提示和提示程序作为推理工具调用

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#prompts-and-prompt-programs-as-reasoning-tool-calls)

> “Cognitive tools” encapsulate reasoning operations within the LLM itself — [IBM Zurich](https://www.arxiv.org/pdf/2506.12115)  
> “认知工具”将推理操作封装在法学硕士课程本身中 [——IBM 苏黎世](https://www.arxiv.org/pdf/2506.12115)

[![image](https://private-user-images.githubusercontent.com/208424706/461724761-cd06c3f5-5a0b-4ee7-bbba-2f9f243f70ae.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYxNzI0NzYxLWNkMDZjM2Y1LTVhMGItNGVlNy1iYmJhLTJmOWYyNDNmNzBhZS5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT0yMGM3NWYzYWFkYmJiYmYyMGJjMGQ4ZGExZTQ5NDc0Y2M5MDIwOWVhNDBjZDhhNWE4MjU1OGRjNTc1ZDFiMzI0JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.BoHcz-gdU_HBKFy7OSAW-6lM-w-c40Ub1d1dOburfgg)](https://private-user-images.githubusercontent.com/208424706/461724761-cd06c3f5-5a0b-4ee7-bbba-2f9f243f70ae.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYxNzI0NzYxLWNkMDZjM2Y1LTVhMGItNGVlNy1iYmJhLTJmOWYyNDNmNzBhZS5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT0yMGM3NWYzYWFkYmJiYmYyMGJjMGQ4ZGExZTQ5NDc0Y2M5MDIwOWVhNDBjZDhhNWE4MjU1OGRjNTc1ZDFiMzI0JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.BoHcz-gdU_HBKFy7OSAW-6lM-w-c40Ub1d1dOburfgg)

> **These cognitive tools (structured prompt templates as tool calls) break down the problem by identifying the main concepts at hand, extracting relevant information in the question, and highlighting meaningful properties, theorems, and techniques that might be helpful in solving the problem.  
> 这些认知工具（结构化提示模板作为工具调用）通过识别手头的主要概念、提取问题中的相关信息以及突出显示可能有助于解决问题的有意义的属性、定理和技术来分解问题。**

[![image](https://private-user-images.githubusercontent.com/208424706/461725384-f7ce8605-6fa3-494f-94cd-94e6b23032b6.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYxNzI1Mzg0LWY3Y2U4NjA1LTZmYTMtNDk0Zi05NGNkLTk0ZTZiMjMwMzJiNi5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT02MjRjNjhjZWM0YmYzNDc1ZDFlZTFlMzFhYmI3Yzg1YmU2M2UyZTE2YWNiNThlM2I3ZDc3OGE3OGU1ZjQ2ODViJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.HYZXPVMT31XODn4-pSLjqfBEom0YCCOD6R3VDRgdPJk)](https://private-user-images.githubusercontent.com/208424706/461725384-f7ce8605-6fa3-494f-94cd-94e6b23032b6.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYxNzI1Mzg0LWY3Y2U4NjA1LTZmYTMtNDk0Zi05NGNkLTk0ZTZiMjMwMzJiNi5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT02MjRjNjhjZWM0YmYzNDc1ZDFlZTFlMzFhYmI3Yzg1YmU2M2UyZTE2YWNiNThlM2I3ZDc3OGE3OGU1ZjQ2ODViJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.HYZXPVMT31XODn4-pSLjqfBEom0YCCOD6R3VDRgdPJk)

> **These templates scaffold reasoning layers similar to cognitive mental shortcuts, commonly studied as "heuristics".  
> 这些模板支撑类似于认知心理捷径的推理层，通常被称为“启发式”研究。**

1. **This research shows that breaking complex tasks into modular “cognitive tools” lets AI solve problems more thoughtfully—mirroring how expert humans reason step by step.  
    这项研究表明，将复杂的任务分解为模块化的“认知工具”可以让人工智能更周到地解决问题——模仿人类专家如何一步步推理。**
    
2. **Instead of relying on a single, big prompt, the model calls specialized prompt templates, aka cognitive tools like “understand question,” “recall related,” “examine answer,” and “backtracking”—each handling a distinct mental operation.  
    该模型并不依赖单一的大提示，而是调用专门的提示模板，也就是“理解问题”、“回忆相关”、“检查答案”和“回溯”等认知工具——每个模板处理不同的心理操作。**
    
3. **Cognitive tools work like inner mental shortcuts: the AI picks the right program at each stage and runs it to plan its reasoning and downstream actions before conducting the task for greater accuracy and flexibility.  
    认知工具就像内在的思维捷径：人工智能在每个阶段选择正确的程序并运行它来规划其推理和后续动作，然后再执行任务，以实现更高的准确性和灵活性。**
    
4. **By compartmentalizing reasoning steps into modular blocks, these tools prevent confusion, reduce error, and make the model’s thought process transparent and auditable—even on hard math problems.  
    通过将推理步骤划分为模块，这些工具可以防止混淆，减少错误，并使模型的思维过程透明且可审计——即使在困难的数学问题上也是如此。**
    
5. **This modular approach upgrades both open and closed models—boosting real-world math problem-solving and approaching the performance of advanced RL-trained “reasoning” models, without extra training.  
    这种模块化方法可以升级开放和封闭模型，从而提高现实世界数学问题的解决能力，并接近先进的 RL 训练“推理”模型的性能，而无需额外的训练。**
    
6. **The results suggest that the seeds of powerful reasoning are already inside large language models—cognitive tools simply unlock and orchestrate these abilities, offering a transparent, efficient, and interpretable alternative to black-box tuning.  
    结果表明，强大推理的种子已经存在于大型语言模型中——认知工具只需解锁和协调这些能力，即可为黑盒调整提供一种透明、高效且可解释的替代方案。**
    

## Emergent Symbols  新兴符号

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#emergent-symbols)

## **[Emergent Symbolic Mechanisms Support Abstract Reasoning in Large Language Models - ICML Princeton June 18, 2025  
新兴符号机制支持大型语言模型中的抽象推理 - ICML 普林斯顿 2025 年 6 月 18 日](https://openreview.net/forum?id=y1SnRPDWx4)**

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#emergent-symbolic-mechanisms-support-abstract-reasoning-in-large-language-models---icml-princeton-june-18-2025)

[![image](https://private-user-images.githubusercontent.com/208424706/460379178-76c6e6cb-b65d-4af7-95a5-6d52aee7efc0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYwMzc5MTc4LTc2YzZlNmNiLWI2NWQtNGFmNy05NWE1LTZkNTJhZWU3ZWZjMC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT0xYjJhMDE2MjRkNmJlNDRmMmUyN2YzNjMyZTRkOWFmYjhkOTIwMjZkNjA2MWY3ZmY0ZjMzN2FhN2QxMzhmYTZmJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.eTjwNBYA8_TZH6nTNAPxsyNJ4l90ui4EIJFzrxPnRHo)](https://private-user-images.githubusercontent.com/208424706/460379178-76c6e6cb-b65d-4af7-95a5-6d52aee7efc0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYwMzc5MTc4LTc2YzZlNmNiLWI2NWQtNGFmNy05NWE1LTZkNTJhZWU3ZWZjMC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT0xYjJhMDE2MjRkNmJlNDRmMmUyN2YzNjMyZTRkOWFmYjhkOTIwMjZkNjA2MWY3ZmY0ZjMzN2FhN2QxMzhmYTZmJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.eTjwNBYA8_TZH6nTNAPxsyNJ4l90ui4EIJFzrxPnRHo)

> **TL;DR: A three-stage architecture is identified that supports abstract reasoning in LLMs via a set of emergent symbol-processing mechanisms.  
> TL;DR：确定了一种三阶段架构，通过一组新兴的符号处理机制支持 LLM 中的抽象推理。**

**These include symbolic induction heads, symbolic abstraction heads, and retrieval heads.  
这些包括符号诱导头脑、符号抽象头脑和检索头脑。**

**1. In early layers, symbol abstraction heads convert input tokens to abstract variables based on the relations between those tokens.  
1. 在早期层中，符号抽象头根据输入标记之间的关系将输入标记转换为抽象变量。**

**2. In intermediate layers, symbolic induction heads perform sequence induction over these abstract variables.  
2. 在中间层，符号感应头对这些抽象变量进行序列感应。**

**3. Finally, in later layers, retrieval heads predict the next token by retrieving the value associated with the predicted abstract variable.  
3. 最后，在后面的层中，检索头通过检索与预测的抽象变量相关联的值来预测下一个标记。**

**These results point toward a resolution of the longstanding debate between symbolic and neural network approaches, suggesting that emergent reasoning in neural networks depends on the emergence of symbolic mechanisms.** — [**ICML Princeton**](https://openreview.net/forum?id=y1SnRPDWx4)  
**这些结果有助于解决符号和神经网络方法之间长期存在的争论，表明神经网络中的涌现推理取决于符号机制的涌现。——** [**ICML 普林斯顿**](https://openreview.net/forum?id=y1SnRPDWx4)

[![image](https://private-user-images.githubusercontent.com/208424706/460378744-2428544e-332a-4e32-9070-9f9d8716d491.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYwMzc4NzQ0LTI0Mjg1NDRlLTMzMmEtNGUzMi05MDcwLTlmOWQ4NzE2ZDQ5MS5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jYWU4MzdkZGViNmY2NzEyZjBmZTc0N2ExMzNjMmU4NWMyNjI0ZWI4ZGJiODllNmNiZTkxMDZjNTNkY2M0ODFkJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.IhkSABvZsG2-bcnEzsGU23VGPVKC8YdFcI5jNZpBbGs)](https://private-user-images.githubusercontent.com/208424706/460378744-2428544e-332a-4e32-9070-9f9d8716d491.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE3MDg1NzIsIm5iZiI6MTc1MTcwODI3MiwicGF0aCI6Ii8yMDg0MjQ3MDYvNDYwMzc4NzQ0LTI0Mjg1NDRlLTMzMmEtNGUzMi05MDcwLTlmOWQ4NzE2ZDQ5MS5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwNzA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDcwNVQwOTM3NTJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1jYWU4MzdkZGViNmY2NzEyZjBmZTc0N2ExMzNjMmU4NWMyNjI0ZWI4ZGJiODllNmNiZTkxMDZjNTNkY2M0ODFkJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.IhkSABvZsG2-bcnEzsGU23VGPVKC8YdFcI5jNZpBbGs)

> **Why Useful?  为什么有用？**
> 
> **This supports why Markdown, Json, and similar structured, symbolic formats are more easily LLM parsable  
> 这支持了为什么 Markdown、Json 和类似的结构化符号格式更容易被 LLM 解析**
> 
> **Concept: Collaborate with agents to apply delimiters, syntax, symbols, symbolic words, metaphors and structure to improve reasoning/context/memory/persistence during inference  
> 概念：与代理协作，应用分隔符、语法、符号、象征性词语、隐喻和结构来改善推理过程中的推理/语境/记忆/持久性**

1. **This paper proves that large language models develop their own inner symbolic “logic circuits”—enabling them to reason with abstract variables, not just surface word patterns.  
    本文证明，大型语言模型能够开发自己的内部符号“逻辑电路”——使它们能够用抽象变量进行推理，而不仅仅是表面的词汇模式。**
    
2. **LLMs show a three-stage process: first abstracting symbols from input, then reasoning over these variables, and finally mapping the abstract answer back to real-world tokens.  
    LLM 展示了一个三阶段过程：首先从输入中抽象出符号，然后对这些变量进行推理，最后将抽象的答案映射回现实世界的标记。**
    
3. **These emergent mechanisms mean LLMs don’t just memorize—they actually create internal, flexible representations that let them generalize to new problems and analogies.  
    这些新兴机制意味着 LLM 不仅仅是记忆——它们实际上创建了内部的、灵活的表示，使它们能够推广到新的问题和类比。**
    
4. **Attention heads in early layers act like “symbol extractors,” intermediate heads perform symbolic reasoning, and late heads retrieve the concrete answer—mirroring human-like abstraction and retrieval.  
    早期层的注意力头就像“符号提取器”，中间层的注意力头执行符号推理，而后期的注意力头检索具体的答案——反映类似人类的抽象和检索。**
    
5. **By running targeted experiments and interventions, the authors show these symbolic processes are both necessary and sufficient for abstract reasoning, across multiple models and tasks.  
    通过进行有针对性的实验和干预，作者表明这些符号过程对于跨多个模型和任务的抽象推理是必要且充分的。**
    
6. **The results bridge the historic gap between symbolic AI and neural nets—showing that, at scale, neural networks can invent and use symbolic machinery, supporting real generalization and reasoning.  
    该结果弥合了符号人工智能和神经网络之间的历史差距——表明神经网络可以大规模发明和使用符号机制，支持真正的泛化和推理。**
    

## Under Construction  建设中

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#under-construction)

## Star History  星史

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#star-history)

[![Star History Chart](https://camo.githubusercontent.com/ce186295c0a322f2ec333c5ba27b1563c5175dd94a84073fe9483db94a67ce88/68747470733a2f2f6170692e737461722d686973746f72792e636f6d2f7376673f7265706f733d64617669646b696d61692f436f6e746578742d456e67696e656572696e6726747970653d44617465)](https://www.star-history.com/#davidkimai/Context-Engineering&Date)

## Contributing  贡献

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#contributing)

We welcome contributions! Check out [CONTRIBUTING.md](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/.github/CONTRIBUTING.md) for guidelines.  
欢迎大家贡献！查看 [CONTRIBUTING.md](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/.github/CONTRIBUTING.md) 获取指南。

## License  执照

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#license)

[MIT License  MIT 许可证](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/LICENSE)

## Citation  引文

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#citation)

```bibtex
@misc{context-engineering,
  author = {Context Engineering Contributors},
  title = {Context Engineering: Beyond Prompt Engineering},
  year = {2025},
  publisher = {GitHub},
  url = {https://github.com/davidkimai/context-engineering}
}
```

## Acknowledgements  致谢

[](https://github.com/KashiwaByte/Context-Engineering-Chinese-Bilingual/blob/main/README.md#acknowledgements)

> I've been looking forward to this being conceptualized and formalized as there wasn't a prior established field. Prompt engineering receives quite the stigma and doesn't quite cover what most researchers and I do.  
> 我一直期待着这个领域能够被概念化和正式化，因为之前并没有一个成熟的领域。快速工程（Prompt Engineering）一直以来都不太被看好，而且它并没有完全涵盖我和大多数研究人员的工作。

- [Andrej Karpathy](https://x.com/karpathy/status/1937902205765607626) for coining "context engineering" and inspiring this repo  
    [Andrej Karpathy](https://x.com/karpathy/status/1937902205765607626) 提出了“上下文工程”的概念并启发了此 repo
- All contributors and the open source community  
    所有贡献者和开源社区