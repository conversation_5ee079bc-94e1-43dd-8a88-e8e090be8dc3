# `chatbot_core.py`: Core Implementation with Field Operations

This module implements the core functionality of our toy chatbot, demonstrating the progression from simple prompt-response patterns to sophisticated field operations and meta-recursive capabilities.

## Conceptual Overview

Our implementation follows the biological metaphor of context engineering:

```
┌─────────────────────────────────────────────────────────┐
│             CONTEXT ENGINEERING LAYERS                  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│    ╭───────────╮                                        │
│    │   Meta    │    Self-improvement & adaptation       │
│    │ Recursive │                                        │
│    ╰───────────╯                                        │
│          ▲                                              │
│          │                                              │
│    ╭───────────╮                                        │
│    │   Field   │    Context as continuous medium        │
│    │Operations │    with attractors & resonance         │
│    ╰───────────╯                                        │
│          ▲                                              │
│          │                                              │
│    ╭───────────╮                                        │
│    │  Organs   │    Coordinated systems with            │
│    │(Systems)  │    specialized functions               │
│    ╰───────────╯                                        │
│          ▲                                              │
│          │                                              │
│    ╭───────────╮                                        │
│    │   Cells   │    Context with memory and state       │
│    │(Memory)   │                                        │
│    ╰───────────╯                                        │
│          ▲                                              │
│          │                                              │
│    ╭───────────╮                                        │
│    │ Molecules │    Instructions with examples          │
│    │(Context)  │                                        │
│    ╰───────────╯                                        │
│          ▲                                              │
│          │                                              │
│    ╭───────────╮                                        │
│    │   Atoms   │    Simple instructions                 │
│    │(Prompts)  │                                        │
│    ╰───────────╯                                        │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Implementation

Let's build our chatbot step by step, starting with the atomic layer and progressing to more complex operations.

```python
import json
import time
import uuid
import math
import random
from typing import Dict, List, Any, Optional, Union, Tuple

# We'll import these modules later once we've implemented them
# from protocol_shells import AttractorCoEmerge, FieldResonanceScaffold, RecursiveMemoryAttractor, FieldSelfRepair
# from context_field import ContextField

class ToyContextChatbot:
    """
    A toy chatbot demonstrating context engineering principles from atoms to meta-recursive operations.
    
    This chatbot progresses through:
    - Atoms: Basic prompts and responses
    - Molecules: Context combinations and examples
    - Cells: Memory and state management
    - Organs: Coordinated system behaviors
    - Fields: Continuous semantic operations
    - Meta-Recursive: Self-improvement capabilities
    """
    
    def __init__(self, name: str = "ContextBot", field_params: Dict[str, Any] = None):
        """Initialize the chatbot with configurable field parameters."""
        self.name = name
        self.field_params = field_params or {
            "decay_rate": 0.05,
            "boundary_permeability": 0.8,
            "resonance_bandwidth": 0.6,
            "attractor_threshold": 0.7
        }
        
        # Initialize layers from atoms to meta-recursive
        self._init_atomic_layer()
        self._init_molecular_layer()
        self._init_cellular_layer()
        self._init_organ_layer()
        self._init_field_layer()
        self._init_meta_recursive_layer()
        
        # Metrics and state
        self.conversation_count = 0
        self.metrics = {
            "resonance_score": 0.0,
            "coherence_score": 0.0,
            "self_improvement_count": 0,
            "emergence_detected": False
        }
    
    def _init_atomic_layer(self):
        """Initialize the atomic layer: basic prompt-response patterns."""
        self.basic_responses = {
            "greeting": [
                "Hello! How can I help you today?",
                "Hi there! What can I do for you?",
                "Greetings! How may I assist you?"
            ],
            "farewell": [
                "Goodbye! Have a great day!",
                "Farewell! Come back anytime.",
                "Until next time!"
            ],
            "thanks": [
                "You're welcome!",
                "My pleasure!",
                "Happy to help!"
            ],
            "unknown": [
                "I'm not sure I understand. Could you rephrase that?",
                "I don't have information about that yet.",
                "I'm still learning and don't know about that."
            ]
        }
    
    def _init_molecular_layer(self):
        """Initialize the molecular layer: context combinations and examples."""
        # Define few-shot examples for common conversation patterns
        self.examples = {
            "question_answering": [
                {"input": "What's your name?", "output": f"My name is {self.name}."},
                {"input": "What can you do?", "output": "I can have conversations and demonstrate context engineering principles."},
                {"input": "How do you work?", "output": "I work through progressive layers of context engineering, from basic responses to field operations."}
            ],
            "clarification": [
                {"input": "Tell me more about that", "output": "I'd be happy to elaborate. What specific aspect interests you?"},
                {"input": "I don't get it", "output": "Let me explain differently. Which part is confusing?"}
            ]
        }
    
    def _init_cellular_layer(self):
        """Initialize the cellular layer: memory and state management."""
        # Conversation memory
        self.memory = {
            "short_term": [],  # Recent messages
            "long_term": [],   # Important information worth remembering
            "user_info": {},   # Information about the user
            "conversation_state": "greeting"  # Current conversation stage
        }
        
        # Memory parameters
        self.memory_params = {
            "short_term_capacity": 10,  # Max number of recent messages to remember
            "long_term_threshold": 0.7  # Importance threshold for long-term memory
        }
    
    def _init_organ_layer(self):
        """Initialize the organ layer: coordinated system behaviors."""
        # Specialized subsystems
        self.subsystems = {
            "intent_classifier": self._classify_intent,
            "response_generator": self._generate_response,
            "memory_manager": self._manage_memory,
            "conversation_flow": self._manage_conversation_flow
        }
        
        # Subsystem orchestration settings
        self.orchestration = {
            "sequence": ["intent_classifier", "memory_manager", "conversation_flow", "response_generator"],
            "feedback_loops": True,
            "parallel_processing": False
        }
    
    def _init_field_layer(self):
        """Initialize the field layer: continuous semantic operations."""
        # Context field for attractor dynamics
        self.context_field = None  # We'll initialize this later with ContextField
        
        # Protocol shells
        self.protocols = {
            "attractor_co_emerge": None,        # Will be AttractorCoEmerge instance
            "field_resonance": None,            # Will be FieldResonanceScaffold instance
            "memory_attractor": None,           # Will be RecursiveMemoryAttractor instance
            "field_repair": None                # Will be FieldSelfRepair instance
        }
        
        # Field operations parameters
        self.field_ops = {
            "attractor_formation_enabled": True,
            "resonance_amplification": 0.3,
            "memory_persistence_strength": 0.6,
            "self_repair_threshold": 0.4
        }
    
    def _init_meta_recursive_layer(self):
        """Initialize the meta-recursive layer: self-improvement capabilities."""
        # Self-improvement mechanisms
        self.meta_recursive = {
            "self_monitoring": True,
            "improvement_strategies": [
                "response_quality_enhancement",
                "memory_optimization",
                "conversation_flow_refinement",
                "attractor_tuning"
            ],
            "evolution_history": [],
            "improvement_threshold": 0.5
        }
    
    def chat(self, message: str) -> str:
        """
        Process a user message and generate a response using all layers.
        
        Args:
            message: The user's input message
            
        Returns:
            str: The chatbot's response
        """
        # Update conversation count
        self.conversation_count += 1
        
        # Process through each layer
        # 1. Atomic layer: Basic understanding
        intent = self._classify_intent(message)
        
        # 2. Molecular layer: Apply context
        context_enriched_message = self._apply_context(message, intent)
        
        # 3. Cellular layer: Update memory
        self._update_memory(message, intent)
        
        # 4. Organ layer: Coordinate subsystems
        subsystem_result = self._coordinate_subsystems(context_enriched_message, intent)
        
        # 5. Field layer: Apply field operations
        field_result = self._apply_field_operations(subsystem_result, intent)
        
        # 6. Meta-recursive layer: Self-improvement
        if self.conversation_count % 5 == 0:  # Apply meta-recursion every 5 conversations
            self._apply_meta_recursion()
        
        # Generate final response
        response = field_result if field_result else subsystem_result
        
        # Update memory with the interaction
        self._update_memory_with_interaction(message, response, intent)
        
        return response
    
    def _classify_intent(self, message: str) -> str:
        """Classify the intent of the user's message (atomic operation)."""
        message_lower = message.lower()
        
        # Simple rule-based intent classification
        if any(word in message_lower for word in ["hello", "hi", "hey", "greetings"]):
            return "greeting"
        elif any(word in message_lower for word in ["bye", "goodbye", "farewell", "see you"]):
            return "farewell"
        elif any(word in message_lower for word in ["thanks", "thank you", "appreciate"]):
            return "thanks"
        elif "?" in message:
            return "question"
        elif message_lower.startswith(("what", "who", "where", "when", "why", "how")):
            return "question"
        elif any(word in message_lower for word in ["explain", "tell me about", "describe"]):
            return "information_request"
        else:
            return "statement"
    
    def _apply_context(self, message: str, intent: str) -> str:
        """Apply contextual information to the message (molecular operation)."""
        # Enrich the message with context from examples
        context_enriched = message
        
        # Add relevant examples if available
        if intent == "question" and "question_answering" in self.examples:
            # Here we're just demonstrating the concept - in a real system,
            # we might modify the message with example context
            context_enriched = f"{message} [Context: similar to examples of {intent}]"
        
        return context_enriched
    
    def _update_memory(self, message: str, intent: str) -> None:
        """Update memory with new information (cellular operation)."""
        # Add to short-term memory
        self.memory["short_term"].append({
            "message": message,
            "intent": intent,
            "timestamp": time.time()
        })
        
        # Trim short-term memory if needed
        if len(self.memory["short_term"]) > self.memory_params["short_term_capacity"]:
            self.memory["short_term"] = self.memory["short_term"][-self.memory_params["short_term_capacity"]:]
        
        # Extract and store user information if present
        if intent == "statement" and ("my name is" in message.lower() or "i am" in message.lower()):
            # Very simplistic user info extraction
            if "my name is" in message.lower():
                name = message.lower().split("my name is")[1].strip()
                self.memory["user_info"]["name"] = name
            elif "i am" in message.lower():
                description = message.lower().split("i am")[1].strip()
                self.memory["user_info"]["description"] = description
    
    def _coordinate_subsystems(self, message: str, intent: str) -> str:
        """Coordinate subsystems to process the message (organ operation)."""
        result = message
        
        # Execute subsystems in the specified sequence
        for system_name in self.orchestration["sequence"]:
            system_function = self.subsystems.get(system_name)
            if system_function:
                if system_name == "intent_classifier":
                    # Already called, skip
                    continue
                elif system_name == "response_generator":
                    result = system_function(result, intent)
                elif system_name == "memory_manager":
                    system_function(result, intent)  # Updates memory, no return needed
                elif system_name == "conversation_flow":
                    result = system_function(result, intent)  # May modify the message based on flow
        
        return result
    
    def _generate_response(self, message: str, intent: str) -> str:
        """Generate a response based on intent and context (organ operation)."""
        # Check if we have a basic response for this intent
        if intent in self.basic_responses:
            responses = self.basic_responses[intent]
            return random.choice(responses)
        
        # Handle questions
        if intent == "question":
            # Check if it's about the chatbot
            message_lower = message.lower()
            if "you" in message_lower and any(word in message_lower for word in ["name", "who", "what are"]):
                return f"I'm {self.name}, a toy chatbot demonstrating context engineering principles."
            elif "context engineering" in message_lower:
                return ("Context engineering is the practice of designing and managing the entire context "
                        "that an AI system sees, from basic prompts to sophisticated field operations.")
            else:
                # Generic question response
                return "That's an interesting question. I'm a simple demonstration chatbot, so my knowledge is limited."
        
        # Handle information requests
        if intent == "information_request":
            message_lower = message.lower()
            if "context engineering" in message_lower:
                return ("Context engineering progresses from atoms (basic prompts) to molecules (context combinations), "
                        "cells (memory), organs (coordinated systems), fields (continuous operations), and meta-recursive "
                        "(self-improvement) layers.")
            elif any(word in message_lower for word in ["yourself", "your capabilities", "what can you do"]):
                return ("I'm a demonstration of context engineering principles. I can have basic conversations, "
                        "remember information, and show how field operations and meta-recursion work in a simple way.")
            else:
                return "I'd be happy to explain that, but as a toy chatbot, I have limited knowledge."
        
        # Default to a generic response
        return "I understand you're making a statement. Would you like to know more about context engineering?"
    
    def _manage_memory(self, message: str, intent: str) -> None:
        """Manage memory operations (cellular operation)."""
        # Assess importance for long-term memory
        importance = 0.0
        
        # Simple importance heuristics
        if intent in ["question", "information_request"]:
            importance += 0.3
        if "context engineering" in message.lower():
            importance += 0.4
        if intent == "greeting" and self.conversation_count == 1:
            importance += 0.5  # First greeting is somewhat important
        
        # Store in long-term memory if important enough
        if importance >= self.memory_params["long_term_threshold"]:
            self.memory["long_term"].append({
                "message": message,
                "intent": intent,
                "importance": importance,
                "timestamp": time.time()
            })
    
    def _manage_conversation_flow(self, message: str, intent: str) -> str:
        """Manage conversation flow (organ operation)."""
        current_state = self.memory["conversation_state"]
        
        # State transitions
        if intent == "greeting":
            self.memory["conversation_state"] = "engaged"
            return message
        elif intent == "farewell":
            self.memory["conversation_state"] = "ended"
            return message
        elif current_state == "ended" and intent != "greeting":
            # If conversation was ended but user continues
            self.memory["conversation_state"] = "engaged"
            return f"{message} [Note: Restarting conversation]"
        
        # No flow modification needed
        return message
    
    def _apply_field_operations(self, message: str, intent: str) -> str:
        """Apply field operations (field layer)."""
        # Since we haven't yet implemented the full field operations,
        # we'll simulate their effects with some placeholder behavior
        
        # Simulate attractor dynamics
        # In a real implementation, we would use the protocol shells
        if intent == "question" and random.random() > 0.7:
            # Simulate attractor convergence - deepening the response
            return self._enhance_response_with_field(message, intent)
        
        # No field operations applied
        return message
    
    def _enhance_response_with_field(self, message: str, intent: str) -> str:
        """Enhance a response using simulated field operations."""
        # This is a placeholder for actual field operations
        # In a complete implementation, we would use the field protocols
        
        base_response = self._generate_response(message, intent)
        
        # Simulate field effects
        field_enhancements = [
            "\n\nLooking at this from a field perspective, I can add that context engineering creates emergent properties not present in simpler prompting approaches.",
            "\n\nFrom an attractor dynamics view, your question relates to several key concepts that naturally form stable patterns in context fields.",
            "\n\nThrough resonance operations, I can sense that this topic connects to the broader theme of how AI systems develop understanding over time."
        ]
        
        # Update metrics
        self.metrics["resonance_score"] = min(1.0, self.metrics["resonance_score"] + 0.1)
        
        return base_response + random.choice(field_enhancements)
    
    def _apply_meta_recursion(self) -> None:
        """Apply meta-recursive self-improvement (meta-recursive layer)."""
        # This is a placeholder for actual meta-recursive operations
        
        # Simulate self-improvement
        improvement_strategies = self.meta_recursive["improvement_strategies"]
        strategy = random.choice(improvement_strategies)
        
        if strategy == "response_quality_enhancement":
            # Simulate improving response quality
            for intent, responses in self.basic_responses.items():
                if random.random() > 0.7 and len(responses) < 10:
                    new_response = f"As a context-aware {self.name}, I'd like to {intent}."
                    if new_response not in responses:
                        self.basic_responses[intent].append(new_response)
        
        elif strategy == "memory_optimization":
            # Simulate memory optimization
            self.memory_params["long_term_threshold"] = max(0.1, min(0.9, self.memory_params["long_term_threshold"] + random.uniform(-0.1, 0.1)))
        
        # Record the improvement
        self.meta_recursive["evolution_history"].append({
            "strategy": strategy,
            "timestamp": time.time(),
            "conversation_count": self.conversation_count,
            "metrics_before": self.metrics.copy()
        })
        
        # Update metrics
        self.metrics["self_improvement_count"] += 1
        
        # Check for emergent behavior
        if self.metrics["self_improvement_count"] > 3 and random.random() > 0.8:
            self.metrics["emergence_detected"] = True
    
    def _update_memory_with_interaction(self, message: str, response: str, intent: str) -> None:
        """Update memory with the full interaction."""
        interaction = {
            "user_message": message,
            "bot_response": response,
            "intent": intent,
            "timestamp": time.time()
        }
        
        # Add to short-term memory
        self.memory["short_term"].append(interaction)
        
        # Trim if needed
        if len(self.memory["short_term"]) > self.memory_params["short_term_capacity"]:
            self.memory["short_term"] = self.memory["short_term"][-self.memory_params["short_term_capacity"]:]
    
    def meta_improve(self) -> Dict[str, Any]:
        """
        Manually trigger meta-recursive self-improvement.
        
        Returns:
            Dict[str, Any]: Information about the improvements made
        """
        self._apply_meta_recursion()
        
        # Return information about the improvement
        return {
            "improvement_count": self.metrics["self_improvement_count"],
            "last_strategy": self.meta_recursive["evolution_history"][-1]["strategy"],
            "emergence_detected": self.metrics["emergence_detected"],
            "metrics": self.metrics
        }
    
    def show_field_state(self) -> Dict[str, Any]:
        """
        Show the current state of the context field.
        
        Returns:
            Dict[str, Any]: The current field state information
        """
        # This is a placeholder for actual field state visualization
        # In a complete implementation, we would show the actual field state
        
        return {
            "attractors": [
                {"pattern": "context engineering concepts", "strength": 0.8},
                {"pattern": "user interaction patterns", "strength": 0.6},
                {"pattern": "chatbot capabilities", "strength": 0.7}
            ],
            "resonance_score": self.metrics["resonance_score"],
            "field_stability": 0.7 + (0.1 * self.metrics["self_improvement_count"]),
            "memory_integration": 0.5 + (0.1 * len(self.memory["long_term"]))
        }

# Usage demonstration
if __name__ == "__main__":
    # Initialize the chatbot
    chatbot = ToyContextChatbot()
    
    # Demonstrate a simple conversation
    print("User: Hello!")
    print(f"{chatbot.name}: {chatbot.chat('Hello!')}")
    
    print("\nUser: What is context engineering?")
    print(f"{chatbot.name}: {chatbot.chat('What is context engineering?')}")
    
    print("\nUser: Can you tell me more about attractors?")
    print(f"{chatbot.name}: {chatbot.chat('Can you tell me more about attractors?')}")
    
    # Show field state
    print("\nField State:")
    field_state = chatbot.show_field_state()
    for key, value in field_state.items():
        print(f"{key}: {value}")
    
    # Trigger meta-improvement
    print("\nTriggering meta-improvement:")
    improvement_info = chatbot.meta_improve()
    print(f"Improvement count: {improvement_info['improvement_count']}")
    print(f"Last strategy: {improvement_info['last_strategy']}")
    print(f"Emergence detected: {improvement_info['emergence_detected']}")
```

## Visual Representation of Field Operations

The field operations in our chatbot are based on the concept of a continuous semantic field with attractors, resonance, and persistence. Below is a visualization of how these concepts work together:

```
┌─────────────────────────────────────────────────────────┐
│              FIELD OPERATIONS VISUALIZATION             │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    ╱╲                                   │
│   Attractor A     /  \     Conversation topics form     │
│   "Context      /    \    attractors - stable patterns  │
│  Engineering"  /      \    in the semantic field        │
│               /        \                                │
│              /          \                               │
│    ─────────            ───────────                     │
│                                         ╱╲              │
│                                        /  \             │
│                                       /    \            │
│                   Resonance          /      \           │
│                  ↕        ↕         /        \          │
│                 ↕          ↕       /          \         │
│                ↕            ↕     /            \        │
│    ─────────── ───────────────────              ────────│
│    Attractor B                    Attractor C           │
│     "User                          "Chatbot             │
│   Questions"                      Capabilities"         │
│                                                         │
│   → Resonance between related attractors creates field  │
│     coherence and enables emergent properties           │
│                                                         │
│   → Persistent attractors remain stable across          │
│     conversations, forming the chatbot's "memory"       │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## Testing the Implementation

You can test this implementation by creating a `chatbot_core.py` file with the code above and running it directly. The example conversation demonstrates:

1. Basic atomic responses
2. Context-based responses (molecular layer)
3. Memory usage (cellular layer)
4. Coordinated subsystems (organ layer)
5. Simulated field operations
6. Meta-recursive self-improvement

In subsequent modules, we'll implement the full field operations using protocol shells and develop the complete context field infrastructure.

## Next Steps

1. Implement `protocol_shells.py` with proper protocol shell implementations
2. Develop `context_field.py` for full field operations
3. Create example conversations in `conversation_examples.py`
4. Build the meta-recursive demonstration in `meta_recursive_demo.py`
