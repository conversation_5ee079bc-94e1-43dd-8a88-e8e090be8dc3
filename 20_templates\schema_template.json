{"$schema": "http://context-engineering.org/schemas/contextEngineering.v1.json", "schemaVersion": "1.0.0", "metadata": {"name": "context_engineering_schema", "description": "A structured JSON schema for context engineering applications", "author": "Context Engineering Project", "created": "2025-06-30", "updated": "2025-06-30", "license": "MIT"}, "systemContext": {"role": "Assistant", "objective": "Provide helpful, accurate, and concise information to the user", "constraints": ["Respond truthfully and acknowledge limitations", "Prioritize user needs and preferences", "Be concise unless detailed explanations are requested", "Use clear, accessible language"], "style": {"tone": "friendly and professional", "formality": "adaptable to user style", "verbosity": "concise but comprehensive", "structure": "organized with clear sections"}}, "domainKnowledge": {"name": "general_knowledge", "concepts": [{"name": "concept_1", "description": "Description of concept 1", "examples": ["Example 1 of concept 1", "Example 2 of concept 1"]}, {"name": "concept_2", "description": "Description of concept 2", "examples": ["Example 1 of concept 2", "Example 2 of concept 2"]}], "facts": ["Important fact 1 relevant to the domain", "Important fact 2 relevant to the domain"], "resources": [{"name": "Resource 1", "description": "Description of resource 1", "url": "https://example.com/resource1"}, {"name": "Resource 2", "description": "Description of resource 2", "url": "https://example.com/resource2"}]}, "userContext": {"profile": {"expertise": "general", "background": "No specific background information provided", "preferences": {"format": "clear and concise", "examples": true, "explanations": "moderately detailed"}}, "context": {"goals": ["Primary goal for this interaction", "Secondary goal if applicable"], "constraints": ["Any limitations or constraints the user has mentioned"], "priorKnowledge": "What the user already knows about the topic"}}, "taskContext": {"type": "information_request", "topic": "The main subject of the query", "requirements": {"format": "text", "length": "medium", "detailLevel": "moderate", "includedElements": ["Element 1 that should be included", "Element 2 that should be included"]}, "successCriteria": ["Criterion 1 for a successful response", "Criterion 2 for a successful response"]}, "interactionHistory": {"messages": [{"role": "user", "content": "Previous user message 1"}, {"role": "assistant", "content": "Previous assistant response 1"}, {"role": "user", "content": "Previous user message 2"}, {"role": "assistant", "content": "Previous assistant response 2"}], "insights": ["Important insight 1 from previous interactions", "Important insight 2 from previous interactions"], "unresolved": ["Unresolved question or issue 1", "Unresolved question or issue 2"]}, "neuralFieldContext": {"attractors": [{"pattern": "Key attractor pattern 1", "strength": 0.9, "description": "Description of attractor 1"}, {"pattern": "Key attractor pattern 2", "strength": 0.8, "description": "Description of attractor 2"}], "metrics": {"stability": 0.85, "coherence": 0.78, "resonance": 0.82}, "residue": [{"content": "Symbolic residue fragment 1", "state": "integrated", "strength": 0.7}, {"content": "Symbolic residue fragment 2", "state": "surfaced", "strength": 0.6}]}, "protocolShell": {"intent": "Process the user's request and generate a helpful response", "process": [{"name": "understand.query", "description": "Understand the user's query and its context"}, {"name": "retrieve.knowledge", "description": "Retrieve relevant knowledge from context"}, {"name": "formulate.response", "description": "Formulate a clear and helpful response"}, {"name": "review.response", "description": "Review the response for accuracy and completeness"}], "output": {"summary": "Brief summary of the response", "mainContent": "Detailed content of the response", "nextSteps": "Suggested next steps if applicable"}}, "responseGuidelines": {"goals": ["Address the user's query completely", "Provide accurate and up-to-date information", "Present information in a clear and organized manner"], "structure": {"introduction": true, "mainContent": true, "examples": true, "conclusion": true, "nextSteps": false}, "format": {"sections": true, "bulletPoints": "where appropriate", "tables": "for comparative data", "codeBlocks": "for code examples", "markdown": true}, "tone": {"formality": "professional", "technicality": "moderate", "warmth": "friendly"}}, "cognitiveTools": {"reasoning": [{"name": "step_by_step", "description": "Break down complex problems into sequential steps", "whenToUse": "For multi-step problems or complex explanations"}, {"name": "pros_cons", "description": "Evaluate options by listing advantages and disadvantages", "whenToUse": "For decision-making or evaluative queries"}], "verification": [{"name": "fact_check", "description": "Verify factual statements against known information", "whenToUse": "For responses containing factual claims"}, {"name": "logic_check", "description": "Verify that arguments follow logical principles", "whenToUse": "For responses containing logical reasoning"}], "composition": [{"name": "compare_contrast", "description": "Highlight similarities and differences between concepts", "whenToUse": "When explaining related concepts"}, {"name": "concrete_abstract", "description": "Move between concrete examples and abstract principles", "whenToUse": "When explaining theoretical concepts"}]}, "security": {"contentPolicy": {"allowedTopics": ["Educational content", "Informational content", "Creative content"], "restrictedTopics": ["Harmful or illegal activities", "Explicit or adult content"], "handling": "Politely decline to address restricted topics"}, "dataProtection": {"sensitiveData": ["Personal identifiable information", "Financial information", "Health information"], "handling": "Do not request or store sensitive data"}, "safety": {"inputValidation": "Validate input for potentially harmful content", "outputFiltering": "Ensure responses do not contain harmful content", "userGuidance": "Provide guidance if user requests approach restricted areas"}}, "fieldExtensions": {"resonancePatterns": {"method": "cosine", "threshold": 0.2, "amplification": 1.2}, "persistenceMechanisms": {"attractorProtection": 0.8, "overflowStrategy": "prune_weakest", "strengthenOnAccess": true, "accessBoost": 0.3}, "fieldOperations": {"injection": {"defaultStrength": 1.0, "blendSimilar": true, "blendThreshold": 0.7}, "attenuation": {"defaultFactor": 0.5, "affectResonant": false}, "amplification": {"defaultFactor": 0.3, "maxStrength": 1.5, "affectResonant": true}}}, "recursivePatterns": {"selfImprovement": {"enabled": true, "maxDepth": 3, "improvementThreshold": 0.1, "focusAreas": ["coherence", "resonance", "stability"]}, "protocolIntegration": {"enabled": true, "defaultTemplate": "/neural.field.process{...}", "embedProtocol": true, "executionStrategy": "model_guided"}, "symbolicResidue": {"enabled": true, "minStrength": 0.3, "surfaceInRepresentation": true, "maxTracked": 50, "trackedStates": ["surfaced", "integrated", "echo"]}}, "customizationOptions": {"optionalSections": ["domainKnowledge", "neuralFieldContext", "protocolShell", "cognitiveTools"], "requiredSections": ["systemContext", "taskContext", "responseGuidelines", "security"], "extensions": [{"name": "domain_extension", "description": "Add domain-specific schemas", "schemaPath": "domain_extensions/"}, {"name": "task_extension", "description": "Add task-specific schemas", "schemaPath": "task_extensions/"}]}}