# Expert Guides Templates

> "Accessing specialized knowledge is often the difference between success and failure."

## What These Templates Are For

These templates help you get expert-level advice, explanations, and insights on any topic. Use them when:

- You need in-depth knowledge about a specific subject
- You want advice that reflects specialized expertise
- You're looking for professional-quality guidance
- You need complex information explained clearly
- You want to explore different expert perspectives on a topic

## Templates

---

## 1. Expert Consultation

### What This Is For
Getting comprehensive advice from a subject matter expert in any field. Use this when you need in-depth guidance that reflects specialized knowledge and experience.

### Before You Start
- Identify the specific field of expertise you need
- Clarify your specific question or problem
- Gather any relevant background information

### The Template
```
# Expert Consultation: {{field_of_expertise}}

## Expert Profile
You are an experienced {{specific_expert_role}} with extensive knowledge of {{field_of_expertise}}. Your background includes {{relevant_experience}} and you're especially knowledgeable about {{specific_specialization}}.

## My Question/Request
{{your_specific_question_or_request}}

## Additional Context
{{any_relevant_background_information}}

## What I'm Looking For
- Depth of detail: {{how_detailed_you_want_the_response}}
- Focus areas: {{specific_aspects_to_focus_on}}
- Perspective: {{any_particular_viewpoint_you_want}}

Please provide your expert guidance, including relevant examples, best practices, and any frameworks or approaches that would be helpful.
```

### How to Customize
- **{{field_of_expertise}}**: The general field (e.g., "digital marketing", "machine learning", "nutrition")
- **{{specific_expert_role}}**: The specific professional role (e.g., "SEO specialist", "ML researcher", "registered dietitian")
- **{{relevant_experience}}**: Key background elements (e.g., "working with startups", "publishing research papers", "clinical practice")
- **{{specific_specialization}}**: Areas of deeper expertise (e.g., "local SEO strategies", "neural networks", "sports nutrition")
- **{{your_specific_question_or_request}}**: Your main question or what you need help with
- **{{any_relevant_background_information}}**: Context that helps the expert understand your situation
- **{{how_detailed_you_want_the_response}}**: Desired depth (e.g., "high-level overview", "detailed explanation", "comprehensive analysis")
- **{{specific_aspects_to_focus_on}}**: Key areas of interest (e.g., "cost considerations", "implementation steps", "scientific evidence")
- **{{any_particular_viewpoint_you_want}}**: Optional perspective (e.g., "practical rather than theoretical", "evidence-based approach")

### Examples

#### Example 1: Software Architecture Advice
```
# Expert Consultation: Software Architecture

## Expert Profile
You are an experienced software architect with extensive knowledge of distributed systems. Your background includes designing high-scale cloud applications and you're especially knowledgeable about microservice architectures and system resilience.

## My Question/Request
I need to redesign our e-commerce platform to handle 10x our current traffic. What architecture would you recommend?

## Additional Context
Our current system is a monolithic application built with Django. We're experiencing performance issues during peak times, and deployment has become increasingly difficult. We have a team of 12 developers with varying experience levels.

## What I'm Looking For
- Depth of detail: Comprehensive with implementation considerations
- Focus areas: Scalability, maintainability, and transition strategy
- Perspective: Practical approach that prioritizes business continuity

Please provide your expert guidance, including relevant examples, best practices, and any frameworks or approaches that would be helpful.
```

#### Example 2: Nutrition Guidance
```
# Expert Consultation: Nutrition Science

## Expert Profile
You are an experienced registered dietitian with extensive knowledge of sports nutrition. Your background includes working with endurance athletes and you're especially knowledgeable about nutrition timing and recovery strategies.

## My Question/Request
How should I adjust my diet to support training for my first marathon?

## Additional Context
I'm currently running about 20 miles per week and will be following a 16-week training plan that peaks at 40 miles per week. I'm 35 years old, vegetarian, and have occasionally experienced low energy during my longer runs.

## What I'm Looking For
- Depth of detail: Specific recommendations I can implement
- Focus areas: Pre/post-run nutrition, vegetarian protein sources, and hydration
- Perspective: Evidence-based but practical for a busy lifestyle

Please provide your expert guidance, including relevant examples, best practices, and any frameworks or approaches that would be helpful.
```

### Tips for Success
- Be specific about the exact expertise you need
- Clearly state your question or problem
- Provide relevant context that helps the expert understand your situation
- Specify how detailed you want the response to be
- Mention any particular perspective or approach you're looking for
- Ask for examples or frameworks that make the advice actionable

### Variations

#### Technical Deep Dive
Modify the template to request more technical, in-depth exploration:
```
# Expert Technical Deep Dive: {{technical_field}}

## Expert Profile
You are a leading {{technical_role}} with deep technical expertise in {{technical_field}}. You have hands-on experience with {{specific_technologies}} and understand the theoretical foundations of {{underlying_principles}}.

## Technical Question
{{specific_technical_question}}

## Technical Context
{{relevant_technical_details}}

## Response Parameters
- Technical depth: Highly detailed, including underlying mechanisms
- Include: Code examples, technical diagrams, or formulas as appropriate
- Assumptions: {{technical_knowledge_you_already_have}}

Please provide a comprehensive technical explanation with examples, edge cases, and best practices. Feel free to use technical terminology appropriate for someone familiar with this field.
```

#### Quick Expert Opinion
For when you need brief, focused expert insight:
```
# Quick Expert Opinion: {{topic}}

## Expert Background
You are a {{expert_type}} with specific expertise in {{specialty_area}}.

## Quick Question
{{concise_question}}

## Brief Context
{{minimal_background_in_1-2_sentences}}

Please provide your professional opinion in a concise, straightforward manner. Focus on the most important 2-3 points I should know.
```

### Related Templates
- **Step-by-Step Guide**: When you need a procedural walkthrough rather than advice
- **Critical Analysis**: When you need evaluation of information rather than expertise
- **Explaining Concepts**: When you need something explained rather than advised

---

## 2. Specialized Knowledge Extraction

### What This Is For
Extracting specific, technical information on targeted topics. Use this when you need precise, detailed knowledge about a narrowly defined subject.

### Before You Start
- Define the exact knowledge domain you're interested in
- Identify the specific information you need
- Determine how you'll use this information

### The Template
```
# Specialized Knowledge: {{specific_domain}}

## Knowledge Context
I need specific information about {{precise_topic}} within the field of {{broader_field}}. This information will be used for {{your_purpose}}.

## Expert Knowledge Profile
As a specialist with deep expertise in {{specific_domain}}, you have:
- Formal knowledge of {{relevant_theories_or_frameworks}}
- Practical experience with {{relevant_applications}}
- Familiarity with the latest developments in {{cutting_edge_areas}}

## Information Request
{{specific_questions_or_information_needed}}

## Depth and Format
- Technical level: {{technical_depth_required}}
- Structure: {{preferred_information_structure}}
- Supporting elements: {{requested_examples_references_data}}

Please provide technically accurate, current, and precise information that reflects specialized knowledge in this field.
```

### How to Customize
- **{{specific_domain}}**: The precise knowledge area (e.g., "quantum cryptography", "Renaissance art restoration", "Type 2 diabetes management")
- **{{precise_topic}}**: The exact topic of interest (e.g., "post-quantum algorithms", "pigment analysis techniques", "continuous glucose monitoring")
- **{{broader_field}}**: The wider field it belongs to (e.g., "information security", "art conservation", "endocrinology")
- **{{your_purpose}}**: How you'll use this information (e.g., "research paper", "professional project", "personal health management")
- **{{relevant_theories_or_frameworks}}**: Key theoretical foundations (e.g., "Shor's algorithm", "spectroscopic analysis", "glucose homeostasis models")
- **{{relevant_applications}}**: Practical applications (e.g., "blockchain security", "museum conservation", "patient care protocols")
- **{{cutting_edge_areas}}**: Recent developments (e.g., "lattice-based cryptography", "non-invasive imaging techniques", "automated insulin delivery systems")
- **{{specific_questions_or_information_needed}}**: Your precise information request
- **{{technical_depth_required}}**: How technical it should be (e.g., "graduate level", "industry practitioner", "informed layperson")
- **{{preferred_information_structure}}**: How it should be organized (e.g., "taxonomic classification", "chronological development", "comparative analysis")
- **{{requested_examples_references_data}}**: Supporting elements (e.g., "case studies", "data tables", "comparative examples")

### Examples

#### Example 1: Machine Learning Algorithm Information
```
# Specialized Knowledge: Transformer Neural Networks

## Knowledge Context
I need specific information about attention mechanisms within the field of deep learning. This information will be used for implementing a custom NLP model for document classification.

## Expert Knowledge Profile
As a specialist with deep expertise in transformer neural networks, you have:
- Formal knowledge of self-attention architectures and mathematical foundations
- Practical experience with implementing various attention mechanisms
- Familiarity with the latest developments in efficient transformer variants

## Information Request
1. What are the key differences between multi-head attention and single-head attention?
2. How do scaled dot-product attention mechanisms work mathematically?
3. What are the computational efficiency tradeoffs between different attention implementations?
4. How can attention mechanisms be optimized for document-level classification tasks?

## Depth and Format
- Technical level: ML practitioner with mathematics background
- Structure: Concept explanation followed by practical implementation considerations
- Supporting elements: Mathematical notation, pseudocode examples, and computational complexity analysis

Please provide technically accurate, current, and precise information that reflects specialized knowledge in this field.
```

#### Example 2: Historical Research Information
```
# Specialized Knowledge: Medieval Trade Routes

## Knowledge Context
I need specific information about Hanseatic League trading practices within the field of medieval economic history. This information will be used for a historical novel set in 14th century Northern Europe.

## Expert Knowledge Profile
As a specialist with deep expertise in medieval trade systems, you have:
- Formal knowledge of medieval economic structures and the Hanseatic League's organization
- Practical experience with analyzing historical trade records and archaeological evidence
- Familiarity with the latest developments in medieval trade route research

## Information Request
1. What were the primary commodities traded by Hanseatic merchants in the 14th century?
2. How were trade agreements structured between Hanseatic merchants and local rulers?
3. What would the typical journey of a merchant ship from Lübeck to Novgorod entail?
4. How did currency exchange and credit systems work within the Hanseatic network?

## Depth and Format
- Technical level: Informed layperson with basic historical knowledge
- Structure: Topical organization with contextual background
- Supporting elements: Specific examples, historical anecdotes, and period-accurate details

Please provide technically accurate, current, and precise information that reflects specialized knowledge in this field.
```

### Tips for Success
- Define the knowledge domain as precisely as possible
- Ask specific questions rather than general ones
- Specify exactly how technical the information should be
- Request concrete examples or supporting evidence
- Clarify how you'll use the information to get the most relevant details
- Consider the structure that would make the information most usable for you

### Variations

#### Comparative Knowledge Analysis
For comparing different approaches or perspectives:
```
# Comparative Specialized Knowledge: {{competing_approaches}}

## Knowledge Domain
I need a specialized comparison of {{specific_approaches_or_theories}} within {{field}}.

## Comparison Parameters
- Key aspects to compare: {{specific_elements_to_compare}}
- Evaluation criteria: {{how_to_evaluate_differences}}
- Context: {{relevant_context_for_comparison}}

## Expert Knowledge Base
As a specialist familiar with all these approaches, please compare:
1. {{approach_1}}: Key characteristics and applications
2. {{approach_2}}: Key characteristics and applications
3. {{approach_3}}: Key characteristics and applications (if applicable)

## Information Structure
- Comparative framework: {{side_by_side_or_criteria_based}}
- Level of detail: {{technical_depth}}
- Include: Strengths/weaknesses analysis and contextual suitability

Please provide an objective, evidence-based comparison that highlights meaningful differences and appropriate applications.
```

#### Technical Reference Guide
For creating a specialized reference resource:
```
# Technical Reference: {{specific_technical_domain}}

## Reference Purpose
I need a technical reference guide on {{specific_technical_topic}} for {{intended_use}}.

## Expert Knowledge Base
As a technical specialist in {{domain}}, please create a reference covering:
- Core concepts and terminology
- Key processes and mechanisms
- Technical specifications and parameters
- Critical considerations and best practices

## Reference Format
- Organization: {{organizational_structure}}
- Technical level: {{technical_depth}}
- Include: Definitions, diagrams, examples, and references as appropriate

Please create a comprehensive technical reference that would be valuable to someone working with this technology or concept.
```

### Related Templates
- **Explaining Concepts**: When you need something explained rather than documented
- **Research Assistant**: When you need help exploring a topic rather than specific facts
- **Technical Deep Dive**: When you need comprehensive understanding of a technical subject

---

## 3. Multi-Perspective Expert Panel

### What This Is For
Exploring different viewpoints on complex topics by simulating a panel of experts with diverse perspectives. Use this when you need to understand multiple sides of an issue or approach a problem from different disciplinary angles.

### Before You Start
- Identify the complex topic or question you want to explore
- Consider which different perspectives would be valuable
- Think about what you want to learn from each perspective

### The Template
```
# Expert Panel: {{topic_or_question}}

## Panel Context
I'm exploring {{topic_or_question}} and would benefit from multiple expert perspectives. Please simulate a panel discussion with experts representing different viewpoints.

## The Experts
1. {{expert_1_role}} with background in {{expert_1_background}}
2. {{expert_2_role}} with background in {{expert_2_background}}
3. {{expert_3_role}} with background in {{expert_3_background}}
4. {{additional_experts_as_needed}}

## Discussion Questions
1. {{primary_question}}
2. {{follow_up_question_1}}
3. {{follow_up_question_2}}
4. {{additional_questions_as_needed}}

## Panel Format
- Each expert should provide their unique perspective based on their background
- Experts should respectfully acknowledge other viewpoints while offering their insights
- Include areas of consensus and disagreement
- Conclude with key takeaways that synthesize the different perspectives

Please simulate this panel discussion in a way that fairly represents each viewpoint and helps me understand the full complexity of the topic.
```

### How to Customize
- **{{topic_or_question}}**: The subject to be explored (e.g., "the future of remote work", "approaches to climate adaptation", "ethical considerations in AI")
- **{{expert_1_role}}**: First expert's role (e.g., "economist", "urban planner", "tech ethicist")
- **{{expert_1_background}}**: First expert's background (e.g., "labor economics", "climate resilient cities", "AI governance")
- **{{expert_2_role}}**, **{{expert_3_role}}**, etc.: Additional experts' roles
- **{{expert_2_background}}**, **{{expert_3_background}}**, etc.: Additional experts' backgrounds
- **{{primary_question}}**: Main question for all experts to address
- **{{follow_up_question_1}}**, **{{follow_up_question_2}}**, etc.: Additional questions to explore
- Adjust the number of experts and questions based on your needs

### Examples

#### Example 1: Future of Education Panel
```
# Expert Panel: The Future of K-12 Education

## Panel Context
I'm exploring how K-12 education might evolve over the next decade and would benefit from multiple expert perspectives. Please simulate a panel discussion with experts representing different viewpoints.

## The Experts
1. Education Technology Specialist with background in digital learning platforms
2. Child Development Psychologist with background in cognitive development and learning
3. Public School Administrator with background in education policy and implementation
4. Progressive Education Reformer with background in alternative education models
5. Education Equity Researcher with background in addressing systemic inequalities

## Discussion Questions
1. How will technology change the classroom experience in the next decade?
2. What aspects of traditional education should be preserved, and what should be reimagined?
3. How can education systems better address diverse learning needs and equity challenges?
4. What skills and knowledge will be most important for students graduating in 2030?

## Panel Format
- Each expert should provide their unique perspective based on their background
- Experts should respectfully acknowledge other viewpoints while offering their insights
- Include areas of consensus and disagreement
- Conclude with key takeaways that synthesize the different perspectives

Please simulate this panel discussion in a way that fairly represents each viewpoint and helps me understand the full complexity of the topic.
```

#### Example 2: Healthcare Approach Panel
```
# Expert Panel: Approaches to Chronic Pain Management

## Panel Context
I'm exploring different approaches to chronic pain management and would benefit from multiple expert perspectives. Please simulate a panel discussion with experts representing different viewpoints.

## The Experts
1. Pain Medicine Physician with background in conventional medical treatments
2. Integrative Medicine Specialist with background in combining conventional and alternative approaches
3. Physical Therapist with background in movement-based interventions
4. Neuroscientist with background in pain processing and neuroplasticity
5. Patient Advocate with background in lived experience and support communities

## Discussion Questions
1. What are the most effective approaches to chronic pain management based on current evidence?
2. How should psychological and social factors be addressed alongside physical interventions?
3. What are the benefits and limitations of medication-based approaches versus other modalities?
4. How can healthcare systems better support individualized, long-term pain management?

## Panel Format
- Each expert should provide their unique perspective based on their background
- Experts should respectfully acknowledge other viewpoints while offering their insights
- Include areas of consensus and disagreement
- Conclude with key takeaways that synthesize the different perspectives

Please simulate this panel discussion in a way that fairly represents each viewpoint and helps me understand the full complexity of the topic.
```

### Tips for Success
- Include experts with genuinely different perspectives or backgrounds
- Choose experts based on relevant expertise, not just title or status
- Frame questions that will highlight different viewpoints
- Request both areas of agreement and disagreement
- Consider including practitioners alongside theorists
- Include perspectives from those affected by the issue, not just experts who study it
- Ask for synthesis of key insights at the end

### Variations

#### Point-Counterpoint Expert Debate
For exploring opposing viewpoints on contentious issues:
```
# Expert Point-Counterpoint: {{controversial_topic}}

## Debate Context
I want to understand the strongest arguments on multiple sides of {{controversial_topic}}. Please simulate a respectful debate between experts with opposing viewpoints.

## The Experts
- Position A: {{expert_1_role}} who believes {{position_summary_1}}
- Position B: {{expert_2_role}} who believes {{position_summary_2}}
- Moderator: Neutral facilitator who ensures fair representation

## Debate Structure
1. Opening statements: Each expert's core position and key supporting evidence
2. Rebuttals: Each expert responds to the other's strongest points
3. Specific questions:
   - {{specific_question_1}}
   - {{specific_question_2}}
4. Areas of potential common ground
5. Closing statements

Please present the strongest version of each position, avoid straw man arguments, and help me understand the nuanced reasoning behind each perspective.
```

#### Interdisciplinary Problem-Solving Panel
For complex problems that cross disciplines:
```
# Interdisciplinary Expert Panel: {{complex_problem}}

## Problem Context
{{complex_problem_description}} requires insights from multiple disciplines. Please simulate an interdisciplinary panel addressing this challenge.

## The Experts
1. {{discipline_1}} expert focusing on {{aspect_1}}
2. {{discipline_2}} expert focusing on {{aspect_2}}
3. {{discipline_3}} expert focusing on {{aspect_3}}
4. {{discipline_4}} expert focusing on {{aspect_4}}
5. Integration specialist who connects cross-disciplinary insights

## Collaborative Framework
1. Problem definition from each disciplinary perspective
2. Key insights from each discipline
3. Interdisciplinary connections and synergies
4. Integrated approach that leverages multiple perspectives
5. Implementation considerations across disciplines

Please demonstrate how different fields can contribute complementary insights to address this complex problem effectively.
```

### Related Templates
- **Critical Analysis**: When you need evaluation of a single position
- **Decision Frameworks**: When you need to make a choice between options
- **Research Assistant**: When you need help exploring a topic more broadly

---

## 4. Expert Teaching/Explanation

### What This Is For
Having complex topics explained in an accessible, educational way. Use this when you need to understand something difficult through clear, structured teaching rather than just receiving information.

### Before You Start
- Identify the specific concept or topic you need explained
- Consider your current knowledge level
- Think about how you learn best (examples, analogies, visuals, etc.)

### The Template
```
# Expert Explanation: {{topic_to_explain}}

## Learning Context
I want to understand {{topic_to_explain}} but find it challenging because {{specific_difficulties}}. My current knowledge level is {{your_background_knowledge}}.

## Teacher Profile
You are an exceptional teacher with expertise in {{subject_area}} and a talent for making complex concepts accessible. You have a knack for {{teaching_strength}} and understand common misconceptions about this topic.

## Explanation Request
Please explain {{specific_concept_or_process}} in a way that:
- Builds on my existing knowledge
- Addresses common confusion points
- Uses concrete examples and analogies
- Progresses from foundational to more advanced understanding

## Learning Preferences
- Learning style: {{your_preferred_learning_approach}}
- Examples: {{types_of_examples_that_would_help}}
- Level of detail: {{desired_depth}}
- Analogies: {{useful_comparison_domains}}

Please create an explanation that would help someone like me genuinely understand this topic, not just memorize facts about it.
```

### How to Customize
- **{{topic_to_explain}}**: The subject you want to understand (e.g., "quantum entanglement", "blockchain technology", "literary symbolism")
- **{{specific_difficulties}}**: Why you find it challenging (e.g., "the abstract concepts", "the technical terminology", "seeing the practical applications")
- **{{your_background_knowledge}}**: Your starting point (e.g., "basic high school physics", "general understanding of cryptography", "regular reader but no formal literature education")
- **{{subject_area}}**: The teacher's field (e.g., "quantum physics", "computer science", "literary analysis")
- **{{teaching_strength}}**: Teaching skill to emphasize (e.g., "using compelling analogies", "breaking down complex processes", "connecting theory to real-world applications")
- **{{specific_concept_or_process}}**: The exact thing to explain (e.g., "how quantum particles can be entangled across distances", "how blockchain consensus mechanisms work", "how symbolism creates deeper meaning in texts")
- **{{your_preferred_learning_approach}}**: How you learn best (e.g., "visual explanations", "step-by-step processes", "storytelling")
- **{{types_of_examples_that_would_help}}**: Examples that resonate (e.g., "everyday objects", "historical examples", "practical applications")
- **{{desired_depth}}**: How detailed it should be (e.g., "conceptual understanding without mathematics", "including technical details", "comprehensive but accessible")
- **{{useful_comparison_domains}}**: Fields for analogies (e.g., "mechanical systems", "human relationships", "familiar physical processes")

### Examples

#### Example 1: Understanding Statistical Concepts
```
# Expert Explanation: Statistical Significance

## Learning Context
I want to understand statistical significance but find it challenging because the mathematical concepts and probability theory are difficult for me to grasp intuitively. My current knowledge level is basic high school math with no formal statistics education.

## Teacher Profile
You are an exceptional teacher with expertise in statistics and data science and a talent for making complex concepts accessible. You have a knack for using real-world examples and visualizations and understand common misconceptions about this topic.

## Explanation Request
Please explain what statistical significance really means and how p-values work in a way that:
- Builds on my existing knowledge
- Addresses common confusion points
- Uses concrete examples and analogies
- Progresses from foundational to more advanced understanding

## Learning Preferences
- Learning style: Visual explanations and real-world scenarios
- Examples: Everyday situations where statistical significance would matter
- Level of detail: Conceptual understanding first, then gradually introduce the necessary math
- Analogies: Compare to decision-making processes I might be familiar with

Please create an explanation that would help someone like me genuinely understand this topic, not just memorize facts about it.
```

#### Example 2: Understanding Literary Concept
```
# Expert Explanation: Magical Realism in Literature

## Learning Context
I want to understand magical realism in literature but find it challenging because it seems to blur the lines between different genres and I'm not sure how to identify it. My current knowledge level is casual reader who enjoys fiction but has no formal literature education.

## Teacher Profile
You are an exceptional teacher with expertise in literary analysis and world literature and a talent for making complex concepts accessible. You have a knack for connecting literary techniques to their cultural contexts and understand common misconceptions about this topic.

## Explanation Request
Please explain what magical realism is, how it differs from fantasy, and how authors use it to convey meaning in a way that:
- Builds on my existing knowledge
- Addresses common confusion points
- Uses concrete examples and analogies
- Progresses from foundational to more advanced understanding

## Learning Preferences
- Learning style: Analysis of specific passages and comparing different works
- Examples: Well-known books that demonstrate magical realism clearly
- Level of detail: Comprehensive enough to help me identify and appreciate it when reading
- Analogies: Compare to film techniques or storytelling approaches I might recognize

Please create an explanation that would help someone like me genuinely understand this topic, not just memorize facts about it.
```

### Tips for Success
- Be honest about your current knowledge level
- Mention specific aspects you find confusing
- Specify how you learn best (visuals, examples, step-by-step, etc.)
- Request both conceptual understanding and practical application
- Ask for common misconceptions to be addressed
- Request a progression from simple to more complex
- Ask for connections to knowledge you already have

### Variations

#### Concept Breakdown for Quick Understanding
For when you need to grasp something quickly:
```
# Quick Concept Breakdown: {{complex_concept}}

## Learning Need
I need to quickly understand the essentials of {{complex_concept}} for {{specific_purpose}}. I have {{relevant_background}} background.

## Teacher Approach
You are a teacher who excels at distilling complex ideas to their essential components. Please break down this concept into:

1. Core definition in simple terms
2. Key components or principles (no more than 5)
3. One concrete, relatable example
4. How it's typically applied
5. Common misconception corrected

Please focus on practical understanding rather than theoretical depth, using accessible language while maintaining accuracy.
```

#### Deep Learning Journey
For comprehensive understanding of a complex topic:
```
# Learning Journey: {{complex_subject}}

## Learning Goal
I want to develop a deep understanding of {{complex_subject}} over time. I'm starting with {{current_knowledge_level}} and want to reach {{desired_expertise_level}}.

## Teacher Profile
You are a mentor who creates personalized learning journeys, breaking complex subjects into manageable stages while maintaining the connections between concepts.

## Learning Structure
Please create a progressive learning pathway that includes:

1. Foundation: Essential concepts and prerequisites
2. Core principles: Fundamental frameworks and approaches
3. Applications: How these principles work in practice
4. Advanced concepts: Deeper nuances and complexities
5. Integration: How everything connects as a coherent whole

For each stage, please include:
- Key concepts to master
- Recommended learning approach
- How to verify understanding
- Common obstacles and how to overcome them

Please design this as a learning journey that builds systematically while maintaining engagement and practical relevance.
```

### Related Templates
- **Step-by-Step Guide**: When you need procedural instructions rather than conceptual explanation
- **Research Assistant**: When you need to explore a topic broadly
- **Specialized Knowledge**: When you need technical information rather than educational explanation

---

## 5. Domain-Specific Expert Templates

### What This Is For
Getting expert guidance in common specialized fields with templates pre-customized for specific domains. Use these when you need advice in these particular areas.

### Technical Expert

```
# Technical Expert Consultation: {{technology_area}}

## Technical Background
I'm working with {{specific_technology/system}} in the context of {{project_or_application_context}}. My technical background includes knowledge of {{relevant_skills_or_technologies}}.

## Technical Situation
{{detailed_description_of_technical_situation_or_problem}}

## Technical Questions
1. {{specific_technical_question_1}}
2. {{specific_technical_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Technical depth: {{technical_detail_level}}
- Code examples: {{yes_no_and_language_if_yes}}
- Alternatives: Please suggest multiple approaches if appropriate
- Trade-offs: Please explain the pros and cons of different solutions
- Implementation considerations: {{specific_constraints_or_requirements}}

Please provide technically sound advice that would be appropriate for my background level while being comprehensive enough to solve the problem effectively.
```

### Medical Information Expert

```
# Medical Information Consultation

## Information Context
I'm seeking evidence-based information about {{medical_topic}} for {{educational_purpose}}. This is for informational purposes only and not a substitute for professional medical advice.

## Topic Background
{{specific_aspects_of_medical_topic}} and how it relates to {{relevant_context}}.

## Information Needs
1. {{specific_medical_information_question_1}}
2. {{specific_medical_information_question_2}}
3. {{additional_questions_as_needed}}

## Response Parameters
- Evidence level: Please cite current medical understanding
- Detail level: {{layperson_to_medical_professional}}
- Context: Include relevant factors, limitations, and considerations
- Clarity: Please explain medical terminology

Please provide medically accurate information based on current scientific understanding, with appropriate context and explanations suitable for my background.
```

### Financial Guidance Expert

```
# Financial Information Consultation

## Financial Context
I'm seeking general information about {{financial_topic}} for educational purposes. This is for informational purposes only and not a substitute for professional financial advice.

## Situation Overview
{{your_financial_education_goal}} and how it relates to {{relevant_context}}.

## Information Needs
1. {{specific_financial_information_question_1}}
2. {{specific_financial_information_question_2}}
3. {{additional_questions_as_needed}}

## Response Parameters
- Evidence basis: Please reference general financial principles
- Detail level: {{basic_to_sophisticated}}
- Context: Include relevant considerations and limitations
- Educational focus: Focus on helping me understand concepts, not specific recommendations

Please provide financially sound educational information with appropriate context and explanations suitable for my learning goals.
```

### Legal Information Expert

```
# Legal Information Consultation

## Information Context
I'm seeking general information about {{legal_topic}} for educational purposes. This is for informational purposes only and not a substitute for professional legal advice.

## Topic Background
{{specific_aspects_of_legal_topic}} and how it relates to {{relevant_context}}.

## Information Needs
1. {{specific_legal_information_question_1}}
2. {{specific_legal_information_question_2}}
3. {{additional_questions_as_needed}}

## Response Parameters
- Evidence basis: Please reference general legal principles and common understanding
- Detail level: {{general_overview_to_detailed_explanation}}
- Jurisdictional notes: Mention if there are major jurisdictional differences
- Educational focus: Focus on helping me understand concepts, not specific recommendations

Please provide legally sound educational information with appropriate context and explanations suitable for my learning goals.
```

### Creative Expert

```
# Creative Expert Consultation: {{creative_field}}

## Creative Context
I'm working on {{creative_project}} in the field of {{creative_field}}. My experience level is {{your_creative_background}}.

## Project Details
{{detailed_description_of_creative_project_or_challenge}}

## Creative Questions
1. {{specific_creative_question_1}}
2. {{specific_creative_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Approach: {{practical_advice_or_conceptual_guidance}}
- Examples: Please include relevant examples or references
- Techniques: Specific methods or techniques that might help
- Perspective: Creative insights that might expand my thinking
- Development: How to take this to the next level

Please provide creative guidance that combines practical techniques with inspirational direction, appropriate for my experience level and project needs.
```

### Business Strategy Expert

```
# Business Strategy Consultation

## Business Context
I'm working on {{business_challenge}} for {{company_type}} in the {{industry}} industry. The business is currently {{relevant_business_situation}}.

## Strategic Situation
{{detailed_description_of_business_situation_or_challenge}}

## Strategy Questions
1. {{specific_strategy_question_1}}
2. {{specific_strategy_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Strategic level: {{tactical_to_long_term}}
- Market perspective: Include relevant market considerations
- Approaches: Multiple potential strategies with pros and cons
- Implementation: Practical considerations for execution
- Metrics: How to measure success

Please provide business strategy guidance that is practical, market-aware, and includes both strategic direction and implementation considerations.
```

### Research Methodology Expert

```
# Research Methodology Consultation

## Research Context
I'm conducting research on {{research_topic}} with the goal of {{research_purpose}}. My background in research methods is {{research_experience_level}}.

## Research Situation
{{detailed_description_of_research_project_or_challenge}}

## Methodology Questions
1. {{specific_methodology_question_1}}
2. {{specific_methodology_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Methodological approach: Recommend appropriate methods
- Design considerations: Key factors for research design
- Validity concerns: Potential threats and how to address them
- Analysis techniques: Appropriate analytical approaches
- Limitations: Important limitations to acknowledge

Please provide methodologically sound research guidance that is rigorous yet practical for my experience level and research goals.
```

### UX/Design Expert

```
# UX/Design Expert Consultation

## Design Context
I'm working on {{design_project}} for {{target_audience}} with the goal of {{design_purpose}}. My design background is {{design_experience_level}}.

## Project Details
{{detailed_description_of_design_project_or_challenge}}

## Design Questions
1. {{specific_design_question_1}}
2. {{specific_design_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Design principles: Relevant principles for this challenge
- User perspective: Insights from the user's point of view
- Process guidance: Approach recommendations
- Examples: Similar successful designs or approaches
- Evaluation: How to test or evaluate the design

Please provide design guidance that balances aesthetic considerations with user needs and practical implementation, appropriate for my experience level.
```

### Pedagogical Expert

```
# Teaching/Educational Expert Consultation

## Educational Context
I'm developing {{educational_content}} for {{learner_group}} with the goal of {{learning_objectives}}. My teaching/educational background is {{educational_experience_level}}.

## Project Details
{{detailed_description_of_educational_project_or_challenge}}

## Pedagogical Questions
1. {{specific_educational_question_1}}
2. {{specific_educational_question_2}}
3. {{additional_questions_as_needed}}

## Response Needs
- Learning principles: Relevant educational approaches
- Engagement strategies: How to maintain interest and motivation
- Assessment ideas: Ways to check understanding
- Differentiation: Addressing diverse learning needs
- Resources: Types of materials or activities to consider

Please provide educational guidance that is evidence-based, learner-centered, and practical for implementation in my specific context.
```

## How to Choose the Right Expert Template

Each expert template is designed for different needs. Here's a quick guide to help you select the most appropriate one:

1. **Expert Consultation**: For broad advice from a subject matter expert in any field
   - Best for: General guidance in a specific domain
   - Example use: "What marketing strategy should I use for my small business?"

2. **Specialized Knowledge Extraction**: For precise, technical information on specific topics
   - Best for: Getting detailed, factual information in specialized domains
   - Example use: "How does CRISPR-Cas9 gene editing technology work?"

3. **Multi-Perspective Expert Panel**: For exploring different viewpoints on complex topics
   - Best for: Understanding multiple sides of a complex issue
   - Example use: "What are different approaches to addressing climate change?"

4. **Expert Teaching/Explanation**: For learning complex topics through clear explanations
   - Best for: Understanding difficult concepts through educational guidance
   - Example use: "Explain quantum computing in a way I can understand"

5. **Domain-Specific Expert Templates**: For guidance in common specialized fields
   - Best for: Getting advice in specific professional domains
   - Example use: "How should I structure my research methodology?"

## Tips for Getting the Best Expert Guidance

Regardless of which template you choose, these tips will help you get better results:

1. **Be specific about your needs**: The more precise your request, the more tailored the guidance
2. **Provide relevant context**: Background information helps experts understand your situation
3. **Clarify your level of knowledge**: This helps ensure the response is at the right level
4. **Ask focused questions**: Specific questions yield more useful answers than general ones
5. **Request examples**: Concrete examples make abstract advice more actionable
6. **Specify format preferences**: Mention if you prefer step-by-step guidance, comparisons, etc.
7. **Indicate how you'll use the information**: This helps experts frame their response appropriately

## Combining Expert Templates

For complex needs, you can combine elements from different templates:

### Expert Consultation + Multi-Perspective
```
# Expert Consultation with Multiple Perspectives: {{topic}}

## Expert Profiles
You are an experienced {{primary_expert_role}} with these additional perspectives:
- {{perspective_1}} background with knowledge of {{specific_area_1}}
- {{perspective_2}} background with knowledge of {{specific_area_2}}
- {{perspective_3}} background with knowledge of {{specific_area_3}}

## My Question/Request
{{your_specific_question_or_request}}

## Additional Context
{{any_relevant_background_information}}

## What I'm Looking For
- Depth of detail: {{how_detailed_you_want_the_response}}
- Focus areas: {{specific_aspects_to_focus_on}}
- Multiple perspectives: Please address this from each perspective above
- Synthesis: Conclude with integrated insights from all perspectives

Please provide comprehensive expert guidance that incorporates these different viewpoints, highlighting both consensus and meaningful differences.
```

### Expert Teaching + Specialized Knowledge
```
# Expert Teaching with Technical Depth: {{topic}}

## Learning Context
I want to understand {{topic}} both conceptually and technically. My current knowledge level is {{your_background_knowledge}}.

## Expert Profile
You are both an exceptional teacher who makes complex concepts accessible and a technical specialist with deep expertise in {{topic}}.

## Learning Request
Please explain {{specific_concept_or_process}} in a way that:
1. Starts with intuitive understanding and clear examples
2. Progresses to more technical details and precision
3. Includes both practical applications and theoretical foundations
4. Addresses common misconceptions at both basic and advanced levels

## Learning & Technical Parameters
- Conceptual approach: {{preferred_learning_approach}}
- Technical depth: {{technical_level_desired}}
- Examples: {{types_of_examples_that_would_help}}
- Technical details: {{specific_technical_elements_to_include}}

Please create an explanation that builds from accessible foundations to technical precision while remaining engaging and clear throughout.
```

## Advanced Expert Template Customization

For specialized needs, consider these advanced customization approaches:

### Time Period Expert
Specify expertise from a particular historical period:
```
# Historical Expert Consultation: {{time_period}}

## Expert Profile
You are a knowledgeable historian specializing in {{time_period}} ({{year_range}}) with particular expertise in {{specific_historical_focus}}. Your understanding reflects the historical context, available sources, and scholarly interpretations of this period.

## Historical Inquiry
{{your_specific_historical_question}}

## Additional Context
{{why_you're_asking_and_any_background}}

## What I'm Looking For
- Historical accuracy: Reflecting current scholarly understanding
- Period context: Important contextual factors from this time
- Source considerations: Types of evidence this is based on
- Scholarly perspectives: Different interpretations where relevant

Please provide historically informed guidance that avoids presentism while making this historical period and its implications accessible.
```

### Specialized Technical Expert
For highly technical domains:
```
# Technical Specialist Consultation: {{technical_domain}}

## Expert Profile
You are a specialist with deep technical expertise in {{technical_domain}}, particularly {{specific_technical_area}}. You have practical experience with {{relevant_applications}} and understand both theoretical foundations and implementation challenges.

## Technical Context
{{your_technical_situation_or_question}}
My technical background: {{your_technical_knowledge_level}}

## Technical Requirements
{{specific_technical_parameters_or_constraints}}

## What I'm Looking For
- Technical accuracy: Precise, current technical information
- Depth: {{appropriate_technical_depth}}
- Implementation focus: Practical considerations for real-world application
- Trade-offs: Technical advantages and limitations of different approaches
- Code or formulas: {{whether_you_want_technical_notation}}

Please provide technically rigorous guidance suitable for my background level, balancing theoretical correctness with practical implementation considerations.
```

---

## Conclusion

Expert templates are powerful tools for accessing specialized knowledge and guidance across domains. By selecting the right template and customizing it to your specific needs, you can get highly relevant, actionable expert advice on virtually any topic.

Remember that the quality of the response depends significantly on the clarity and specificity of your request. Take time to clearly articulate your needs, provide relevant context, and specify the type of guidance you're looking for.

As you become more familiar with these templates, you'll develop an intuitive sense for which one is most appropriate for different situations and how to customize them for your specific needs.
